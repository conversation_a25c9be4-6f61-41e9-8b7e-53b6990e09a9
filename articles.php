<?php
// جلب المقالات من قاعدة البيانات
require_once 'config/database.php';

// إعدادات الترقيم
$articlesPerPage = 6;
$currentPage = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$offset = ($currentPage - 1) * $articlesPerPage;

// إعدادات البحث والفلترة
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$category = isset($_GET['category']) ? $_GET['category'] : 'all';
$sort = isset($_GET['sort']) ? $_GET['sort'] : 'newest';

// بناء استعلام البحث
$whereClause = "WHERE a.status = 'published'";
$params = [];
$types = '';

if (!empty($search)) {
    $whereClause .= " AND (a.title LIKE ? OR a.content LIKE ?)";
    $searchParam = "%$search%";
    $params[] = $searchParam;
    $params[] = $searchParam;
    $types .= 'ss';
}

if ($category !== 'all') {
    $whereClause .= " AND c.name = ?";
    $params[] = $category;
    $types .= 's';
}

// ترتيب النتائج
$orderClause = "ORDER BY ";
switch ($sort) {
    case 'oldest':
        $orderClause .= "a.created_at ASC";
        break;
    case 'popular':
        $orderClause .= "a.views DESC";
        break;
    case 'title':
        $orderClause .= "a.title ASC";
        break;
    default: // newest
        $orderClause .= "a.created_at DESC";
        break;
}

// جلب إجمالي عدد المقالات
$countQuery = "SELECT COUNT(*) as total
               FROM articles a
               LEFT JOIN categories c ON a.category_id = c.id
               LEFT JOIN users_d u ON a.author_id = u.id
               $whereClause";

if (!empty($params)) {
    $countStmt = $conn->prepare($countQuery);
    $countStmt->bind_param($types, ...$params);
    $countStmt->execute();
    $totalArticles = $countStmt->get_result()->fetch_assoc()['total'];
} else {
    $totalArticles = $conn->query($countQuery)->fetch_assoc()['total'];
}

$totalPages = ceil($totalArticles / $articlesPerPage);

// جلب المقالات
$articlesQuery = "SELECT a.*, c.name as category_name, u.name as author_name
                  FROM articles a
                  LEFT JOIN categories c ON a.category_id = c.id
                  LEFT JOIN users_d u ON a.author_id = u.id
                  $whereClause
                  $orderClause
                  LIMIT $articlesPerPage OFFSET $offset";

if (!empty($params)) {
    $articlesStmt = $conn->prepare($articlesQuery);
    $articlesStmt->bind_param($types, ...$params);
    $articlesStmt->execute();
    $articlesResult = $articlesStmt->get_result();
} else {
    $articlesResult = $conn->query($articlesQuery);
}

$articles = [];
while ($row = $articlesResult->fetch_assoc()) {
    $articles[] = $row;
}

// جلب التصنيفات للفلترة
$categoriesQuery = "SELECT DISTINCT name FROM categories ORDER BY name";
$categoriesResult = $conn->query($categoriesQuery);
$categories = [];
while ($row = $categoriesResult->fetch_assoc()) {
    $categories[] = $row['name'];
}

// حساب نطاق العرض
$startRange = ($currentPage - 1) * $articlesPerPage + 1;
$endRange = min($currentPage * $articlesPerPage, $totalArticles);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المقالات - سمايل ديزاين لطب الأسنان</title>
    <link rel="stylesheet" href="style/styles.css">
    <link rel="stylesheet" href="style/articles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <i class="fas fa-tooth"></i>
                    <span>سمايل ديزاين</span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.php" class="nav-link">الرئيسية</a></li>
                    <li><a href="index.php#services" class="nav-link">الخدمات</a></li>
                    <li><a href="about.php" class="nav-link">عن المركز</a></li>
                    <li><a href="index.php#team" class="nav-link">الفريق الطبي</a></li>
                    <li><a href="articles.php" class="nav-link active">المقالات</a></li>
                    <li><a href="index.php#contact" class="nav-link">تواصل معنا</a></li>
                </ul>
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>مقالات طبية</h1>
            <p>اكتشف أحدث المعلومات والنصائح حول صحة الأسنان</p>
            <nav class="breadcrumb">
                <a href="index.html">الرئيسية</a>
                <span>/</span>
                <span>المقالات</span>
            </nav>
        </div>
    </section>

    <!-- Articles Section -->
    <section class="articles-section">
        <div class="container">
            <!-- Filters Bar -->
            <div class="filters-bar">
                <div class="search-section">
                    <form method="GET" action="articles.php" class="search-form">
                        <div class="search-box">
                            <input type="text" name="search" placeholder="ابحث في المقالات..."
                                   value="<?php echo htmlspecialchars($search); ?>" id="searchInput">
                            <button type="submit" id="searchBtn">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <input type="hidden" name="category" value="<?php echo htmlspecialchars($category); ?>">
                        <input type="hidden" name="sort" value="<?php echo htmlspecialchars($sort); ?>">
                    </form>
                </div>

                <div class="filter-section">
                    <div class="categories-filter">
                        <label>التصنيف:</label>
                        <select id="categorySelect" onchange="filterArticles()">
                            <option value="all" <?php echo $category === 'all' ? 'selected' : ''; ?>>جميع المقالات</option>
                            <?php foreach ($categories as $cat): ?>
                            <option value="<?php echo htmlspecialchars($cat); ?>"
                                    <?php echo $category === $cat ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <main class="articles-main">
                    <div class="articles-header">
                        <div class="results-info">
                            <span id="resultsCount">
                                عرض <?php echo $startRange; ?>-<?php echo $endRange; ?> من <?php echo $totalArticles; ?> مقال
                                <?php if (!empty($search)): ?>
                                    (البحث عن: "<?php echo htmlspecialchars($search); ?>")
                                <?php endif; ?>
                                <?php if ($category !== 'all'): ?>
                                    (التصنيف: <?php echo htmlspecialchars($category); ?>)
                                <?php endif; ?>
                            </span>
                        </div>
                        <div class="sort-options">
                            <select id="sortSelect" onchange="sortArticles()">
                                <option value="newest" <?php echo $sort === 'newest' ? 'selected' : ''; ?>>الأحدث</option>
                                <option value="oldest" <?php echo $sort === 'oldest' ? 'selected' : ''; ?>>الأقدم</option>
                                <option value="popular" <?php echo $sort === 'popular' ? 'selected' : ''; ?>>الأكثر قراءة</option>
                                <option value="title" <?php echo $sort === 'title' ? 'selected' : ''; ?>>حسب العنوان</option>
                            </select>
                        </div>
                    </div>

                    <div class="articles-grid" id="articlesGrid">
                        <?php if (!empty($articles)): ?>
                            <?php foreach ($articles as $article): ?>
                            <article class="article-card" data-category="<?php echo htmlspecialchars($article['category_name']); ?>"
                                     onclick="window.location.href='article.php?id=<?php echo $article['id']; ?>'"
                                     style="cursor: pointer;">
                                <div class="article-image">
                                    <?php
                                    $imagePath = '';
                                    if (!empty($article['image'])) {
                                        // التحقق من وجود الصورة في مجلد uploads
                                        if (file_exists('uploads/articles/' . $article['image'])) {
                                            $imagePath = 'uploads/articles/' . $article['image'];
                                        } elseif (file_exists('imgs/' . $article['image'])) {
                                            $imagePath = 'imgs/' . $article['image'];
                                        }
                                    }

                                    // إذا لم توجد صورة، استخدم الصورة الافتراضية
                                    if (empty($imagePath)) {
                                        $imagePath = 'imgs/default-article.svg';
                                    }
                                    ?>

                                    <?php if (file_exists($imagePath)): ?>
                                        <img src="<?php echo htmlspecialchars($imagePath); ?>"
                                             alt="<?php echo htmlspecialchars($article['title']); ?>"
                                             style="width: 100%; height: 100%; object-fit: cover;">
                                    <?php else: ?>
                                        <div class="article-placeholder">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                    <?php endif; ?>
                                    <div class="article-category"><?php echo htmlspecialchars($article['category_name']); ?></div>
                                </div>
                                <div class="article-content">
                                    <h3><a href="article.php?id=<?php echo $article['id']; ?>" onclick="event.stopPropagation();"><?php echo htmlspecialchars($article['title']); ?></a></h3>
                                    <p><?php echo htmlspecialchars(substr($article['content'], 0, 150)) . '...'; ?></p>
                                    <div class="article-meta">
                                        <span class="date">
                                            <i class="fas fa-calendar"></i>
                                            <?php echo date('d M Y', strtotime($article['created_at'])); ?>
                                        </span>
                                        <span class="author">
                                            <i class="fas fa-user"></i>
                                            <?php echo htmlspecialchars($article['author_name']); ?>
                                        </span>
                                        <span class="views">
                                            <i class="fas fa-eye"></i>
                                            <?php echo number_format($article['views']); ?>
                                        </span>
                                    </div>
                                    <div class="read-more">
                                        <a href="article.php?id=<?php echo $article['id']; ?>" class="read-more-btn" onclick="event.stopPropagation();">
                                            <i class="fas fa-arrow-left"></i>
                                            اقرأ المزيد
                                        </a>
                                    </div>
                                </div>
                            </article>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="no-articles">
                                <i class="fas fa-file-alt"></i>
                                <h3>لا توجد مقالات</h3>
                                <p>لا توجد مقالات متاحة حالياً أو لا توجد نتائج للبحث المحدد.</p>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if ($totalPages > 1): ?>
                    <div class="pagination-wrapper">
                        <nav class="pagination" id="pagination">
                            <?php if ($currentPage > 1): ?>
                            <a href="?page=<?php echo $currentPage - 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&sort=<?php echo urlencode($sort); ?>"
                               class="page-link prev">
                                <i class="fas fa-chevron-right"></i>
                                السابق
                            </a>
                            <?php else: ?>
                            <span class="page-link prev disabled">
                                <i class="fas fa-chevron-right"></i>
                                السابق
                            </span>
                            <?php endif; ?>

                            <div class="page-numbers" id="pageNumbers">
                                <?php
                                $startPage = max(1, $currentPage - 2);
                                $endPage = min($totalPages, $currentPage + 2);

                                for ($i = $startPage; $i <= $endPage; $i++):
                                ?>
                                <a href="?page=<?php echo $i; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&sort=<?php echo urlencode($sort); ?>"
                                   class="page-link <?php echo $i === $currentPage ? 'active' : ''; ?>" data-page="<?php echo $i; ?>">
                                    <?php echo $i; ?>
                                </a>
                                <?php endfor; ?>
                            </div>

                            <?php if ($currentPage < $totalPages): ?>
                            <a href="?page=<?php echo $currentPage + 1; ?>&search=<?php echo urlencode($search); ?>&category=<?php echo urlencode($category); ?>&sort=<?php echo urlencode($sort); ?>"
                               class="page-link next">
                                التالي
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <?php else: ?>
                            <span class="page-link next disabled">
                                التالي
                                <i class="fas fa-chevron-left"></i>
                            </span>
                            <?php endif; ?>
                        </nav>
                        <div class="pagination-info">
                            <span>الصفحة <span id="currentPageInfo"><?php echo $currentPage; ?></span> من <span id="totalPagesInfo"><?php echo $totalPages; ?></span></span>
                        </div>
                    </div>
                    <?php endif; ?>
            </main>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <i class="fas fa-tooth"></i>
                        <span>سمايل ديزاين لطب الأسنان</span>
                    </div>
                    <p>نقدم أفضل خدمات طب الأسنان بأحدث التقنيات وأعلى معايير الجودة مع تصميم ابتسامة مثالية</p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.html">الرئيسية</a></li>
                        <li><a href="index.html#services">الخدمات</a></li>
                        <li><a href="index.html#about">عن العيادة</a></li>
                        <li><a href="index.html#team">الفريق الطبي</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>خدماتنا</h4>
                    <ul>
                        <li><a href="#">تنظيف الأسنان</a></li>
                        <li><a href="#">تبييض الأسنان</a></li>
                        <li><a href="#">زراعة الأسنان</a></li>
                        <li><a href="#">تقويم الأسنان</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>معلومات التواصل</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-map-marker-alt"></i> شارع الملك فهد، الرياض</p>
                        <p><i class="fas fa-phone"></i> +966 11 123 4567</p>
                        <p><i class="fas fa-envelope"></i> <EMAIL></p>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 سمايل ديزاين لطب الأسنان. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
    <script>
        // JavaScript للتعامل مع الفلترة والترتيب
        function filterArticles() {
            const category = document.getElementById('categorySelect').value;
            const search = document.getElementById('searchInput').value;
            const sort = document.getElementById('sortSelect').value;

            // إنشاء URL جديد مع المعاملات
            const url = new URL(window.location.href);
            url.searchParams.set('category', category);
            url.searchParams.set('search', search);
            url.searchParams.set('sort', sort);
            url.searchParams.set('page', '1'); // العودة للصفحة الأولى

            // إعادة توجيه للصفحة الجديدة
            window.location.href = url.toString();
        }

        function sortArticles() {
            const sort = document.getElementById('sortSelect').value;
            const search = document.getElementById('searchInput').value;
            const category = document.getElementById('categorySelect').value;

            // إنشاء URL جديد مع المعاملات
            const url = new URL(window.location.href);
            url.searchParams.set('sort', sort);
            url.searchParams.set('search', search);
            url.searchParams.set('category', category);
            url.searchParams.set('page', '1'); // العودة للصفحة الأولى

            // إعادة توجيه للصفحة الجديدة
            window.location.href = url.toString();
        }

        // تحديث عداد المشاهدات عند النقر على مقال
        function updateViews(articleId) {
            fetch('php/update-views.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ article_id: articleId })
            });
        }

        // إضافة مستمع للأحداث عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة مستمع للنقر على روابط المقالات
            const articleLinks = document.querySelectorAll('.article-card h3 a');
            articleLinks.forEach(link => {
                link.addEventListener('click', function() {
                    const articleId = this.href.split('id=')[1];
                    if (articleId) {
                        updateViews(articleId);
                    }
                });
            });
        });
    </script>
</body>
</html>
