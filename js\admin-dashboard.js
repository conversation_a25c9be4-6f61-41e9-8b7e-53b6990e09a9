// Admin Dashboard JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
    loadDashboardData();
    setupEventListeners();
});

// Initialize Dashboard
function initializeDashboard() {
    // Setup navigation
    setupNavigation();
    
    // Load initial data
    loadRecentArticles();
    loadRecentDoctors();
    loadCategories();
    
    // Initialize charts
    initializeCharts();
}

// Setup Navigation
function setupNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const sectionId = this.getAttribute('data-section');
            showSection(sectionId);
            
            // Update active nav item
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            this.closest('.nav-item').classList.add('active');
        });
    });
}

// Show Section
function showSection(sectionId) {
    // Hide all sections
    document.querySelectorAll('.admin-section').forEach(section => {
        section.classList.remove('active');
    });

    // تنظيف الرسوم البيانية السابقة
    cleanupCharts();

    // Show target section
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');

        // Load section-specific data
        loadSectionData(sectionId);
    }
}

// تنظيف الرسوم البيانية لمنع التضارب
function cleanupCharts() {
    // تدمير رسوم لوحة المعلومات
    if (window.dashboardCategoriesChartInstance) {
        window.dashboardCategoriesChartInstance.destroy();
        window.dashboardCategoriesChartInstance = null;
    }
    if (window.dashboardViewsChartInstance) {
        window.dashboardViewsChartInstance.destroy();
        window.dashboardViewsChartInstance = null;
    }

    // تدمير رسوم التحليلات
    if (window.categoriesChartInstance) {
        window.categoriesChartInstance.destroy();
        window.categoriesChartInstance = null;
    }
    if (window.viewsChartInstance) {
        window.viewsChartInstance.destroy();
        window.viewsChartInstance = null;
    }
}

// Load Section Data
function loadSectionData(sectionId) {
    switch(sectionId) {
        case 'articles':
            loadArticles();
            break;
        case 'categories':
            loadCategoriesGrid();
            break;
        case 'doctors':
            loadDoctors();
            break;
        case 'services':
            loadServices();
            break;
        case 'analytics':
            loadAnalytics();
            break;
    }
}

// Modal Functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = 'auto';
        
        // Reset form
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    }
}

// Setup Event Listeners
function setupEventListeners() {
    // Form submissions
    setupFormSubmissions();
    
    // Search and filters
    setupSearchAndFilters();
    
    // Modal close on outside click
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal(this.id);
            }
        });
    });
}

// Setup Form Submissions
function setupFormSubmissions() {
    // Article form
    const articleForm = document.getElementById('articleForm');
    if (articleForm) {
        articleForm.addEventListener('submit', handleArticleSubmit);
    }
    
    // Category form
    const categoryForm = document.getElementById('categoryForm');
    if (categoryForm) {
        categoryForm.addEventListener('submit', handleCategorySubmit);
    }
    
    // Doctor form
    const doctorForm = document.getElementById('doctorForm');
    if (doctorForm) {
        doctorForm.addEventListener('submit', handleDoctorSubmit);
    }
    
    // Service form
    const serviceForm = document.getElementById('serviceForm');
    if (serviceForm) {
        serviceForm.addEventListener('submit', handleServiceSubmit);
    }
}

// Handle Form Submissions
function handleArticleSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    formData.append('action', 'add_article');

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast('تم حفظ المقال بنجاح', 'success');
            closeModal('articleModal');
            loadArticles();
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

function handleCategorySubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    formData.append('action', 'add_category');
    
    showLoading();
    
    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast('تم حفظ التصنيف بنجاح', 'success');
            closeModal('categoryModal');
            loadCategoriesGrid();
            loadCategories(); // Reload for dropdowns
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

function handleDoctorSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    formData.append('action', 'add_doctor');
    
    showLoading();
    
    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast('تم حفظ بيانات الطبيب بنجاح', 'success');
            closeModal('doctorModal');
            loadDoctors();
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

function handleServiceSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);
    formData.append('action', 'add_service');
    
    showLoading();
    
    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast('تم حفظ الخدمة بنجاح', 'success');
            closeModal('serviceModal');
            loadServices();
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

// Loading Functions
function showLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('active');
    }
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('active');
    }
}

// Toast Notifications
function showToast(message, type = 'info') {
    const container = document.getElementById('toastContainer');
    if (!container) return;
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    const icon = getToastIcon(type);
    toast.innerHTML = `
        <i class="${icon}"></i>
        <span>${message}</span>
    `;
    
    container.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

function getToastIcon(type) {
    switch(type) {
        case 'success': return 'fas fa-check-circle';
        case 'error': return 'fas fa-exclamation-circle';
        case 'warning': return 'fas fa-exclamation-triangle';
        default: return 'fas fa-info-circle';
    }
}

// Refresh Dashboard
function refreshDashboard() {
    showLoading();

    // Reload all dashboard data
    loadDashboardData();
    loadRecentArticles();
    loadRecentDoctors();

    setTimeout(() => {
        hideLoading();
        showToast('تم تحديث البيانات بنجاح', 'success');
    }, 1000);
}

// Load Dashboard Data
function loadDashboardData() {
    fetch('php/dashboard-data.php?action=stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatsCards(data.stats);
            }
        })
        .catch(error => console.error('Error loading stats:', error));
}

// Update Stats Cards
function updateStatsCards(stats) {
    const statCards = document.querySelectorAll('.stat-card');
    statCards.forEach((card, index) => {
        const h3 = card.querySelector('h3');
        if (h3) {
            switch(index) {
                case 0: h3.textContent = stats.articles || 0; break;
                case 1: h3.textContent = stats.categories || 0; break;
                case 2: h3.textContent = stats.doctors || 0; break;
                case 3: h3.textContent = stats.services || 0; break;
                case 4: h3.textContent = formatNumber(stats.views || 0); break;
            }
        }
    });
}

// Load Recent Articles
function loadRecentArticles() {
    fetch('php/dashboard-data.php?action=recent_articles')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRecentArticles(data.articles);
            }
        })
        .catch(error => console.error('Error loading recent articles:', error));
}

// Display Recent Articles
function displayRecentArticles(articles) {
    const container = document.getElementById('recent-articles');
    if (!container) return;

    if (articles.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6b7280;">لا توجد مقالات حديثة</p>';
        return;
    }

    const html = articles.map(article => `
        <div style="padding: 1rem 0; border-bottom: 1px solid #e5e7eb;">
            <h4 style="margin: 0 0 0.5rem 0; font-size: 1rem; color: #1f2937;">${article.title}</h4>
            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.85rem; color: #6b7280;">
                <span>${article.category_name}</span>
                <span>${formatDate(article.created_at)}</span>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Load Recent Doctors
function loadRecentDoctors() {
    fetch('php/dashboard-data.php?action=recent_doctors')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRecentDoctors(data.doctors);
            }
        })
        .catch(error => console.error('Error loading recent doctors:', error));
}

// Display Recent Doctors
function displayRecentDoctors(doctors) {
    const container = document.getElementById('recent-doctors');
    if (!container) return;

    if (doctors.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6b7280;">لا توجد أطباء مضافون حديثاً</p>';
        return;
    }

    const html = doctors.map(doctor => `
        <div style="padding: 1rem 0; border-bottom: 1px solid #e5e7eb;">
            <h4 style="margin: 0 0 0.5rem 0; font-size: 1rem; color: #1f2937;">${doctor.name}</h4>
            <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.85rem; color: #6b7280;">
                <span>${doctor.specialty}</span>
                <span>${doctor.experience} سنة خبرة</span>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Load Categories
function loadCategories() {
    fetch('php/dashboard-data.php?action=categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCategoryDropdowns(data.categories);
            }
        })
        .catch(error => console.error('Error loading categories:', error));
}

// Update Category Dropdowns
function updateCategoryDropdowns(categories) {
    const dropdowns = document.querySelectorAll('select[name="category_id"]');
    dropdowns.forEach(dropdown => {
        const currentValue = dropdown.value;
        dropdown.innerHTML = '<option value="">اختر التصنيف</option>';

        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            if (category.id == currentValue) {
                option.selected = true;
            }
            dropdown.appendChild(option);
        });
    });

    // Update filter dropdown
    const filterDropdown = document.getElementById('categoryFilter');
    if (filterDropdown) {
        const currentValue = filterDropdown.value;
        filterDropdown.innerHTML = '<option value="">جميع التصنيفات</option>';

        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            if (category.id == currentValue) {
                option.selected = true;
            }
            filterDropdown.appendChild(option);
        });
    }
}

// Advanced Articles Management
let currentPage = 1;
let articlesPerPage = 25;
let currentSort = { field: 'created_at', direction: 'desc' };
let selectedArticles = new Set();

// Load Articles with advanced filtering
function loadArticles() {
    const search = document.getElementById('articleSearch')?.value || '';
    const category = document.getElementById('categoryFilter')?.value || '';
    const status = document.getElementById('statusFilter')?.value || '';
    const author = document.getElementById('authorFilter')?.value || '';
    const dateFrom = document.getElementById('dateFromFilter')?.value || '';
    const dateTo = document.getElementById('dateToFilter')?.value || '';
    const views = document.getElementById('viewsFilter')?.value || '';

    const params = new URLSearchParams({
        action: 'articles_advanced',
        search: search,
        category: category,
        status: status,
        author: author,
        date_from: dateFrom,
        date_to: dateTo,
        views: views,
        page: currentPage,
        per_page: articlesPerPage,
        sort_field: currentSort.field,
        sort_direction: currentSort.direction
    });

    showLoading();

    fetch(`php/dashboard-data.php?${params}`)
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                displayArticlesTable(data.articles);
                updatePagination(data.pagination);
                updateArticlesStats(data.stats);
                updateTableInfo(data.pagination);
            }
        })
        .catch(error => {
            hideLoading();
            console.error('Error loading articles:', error);
        });
}

// Display Articles Table
function displayArticlesTable(articles) {
    const tbody = document.getElementById('articlesTableBody');
    if (!tbody) return;

    if (articles.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #6b7280; padding: 2rem;">لا توجد مقالات تطابق المعايير المحددة</td></tr>';
        return;
    }

    const html = articles.map(article => `
        <tr>
            <td>
                <input type="checkbox" class="article-checkbox" value="${article.id}" onchange="toggleArticleSelection(${article.id})">
            </td>
            <td>
                <div class="article-title-cell">
                    <strong>${article.title}</strong>
                    ${article.featured ? '<span class="featured-badge">مميز</span>' : ''}
                    <div class="article-meta">
                        ${article.excerpt ? `<small>${article.excerpt.substring(0, 100)}...</small>` : ''}
                    </div>
                </div>
            </td>
            <td>
                <span class="category-badge">${article.category_name}</span>
            </td>
            <td>
                <div class="author-cell">
                    <i class="fas fa-user"></i>
                    ${article.author_name}
                </div>
            </td>
            <td>
                <span class="status-badge status-${article.status}">
                    ${getStatusText(article.status)}
                </span>
            </td>
            <td>
                <div class="views-cell">
                    <i class="fas fa-eye"></i>
                    ${formatNumber(article.views)}
                </div>
            </td>
            <td>
                <div class="date-cell">
                    <div>${formatDate(article.created_at)}</div>
                    <small>${formatTime(article.created_at)}</small>
                </div>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="action-btn view" onclick="viewArticle(${article.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="action-btn edit" onclick="editArticle(${article.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn duplicate" onclick="duplicateArticle(${article.id})" title="نسخ">
                        <i class="fas fa-copy"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteArticle(${article.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');

    tbody.innerHTML = html;

    // Update select all checkbox
    updateSelectAllCheckbox();
}

// Utility Functions
function formatNumber(num) {
    return new Intl.NumberFormat('ar-SA').format(num);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function formatTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });
}

function getStatusText(status) {
    switch(status) {
        case 'published': return 'منشور';
        case 'draft': return 'مسودة';
        case 'scheduled': return 'مجدول';
        default: return status;
    }
}

// Articles Selection Functions
function toggleArticleSelection(articleId) {
    if (selectedArticles.has(articleId)) {
        selectedArticles.delete(articleId);
    } else {
        selectedArticles.add(articleId);
    }

    updateBulkActionsPanel();
    updateSelectAllCheckbox();
}

function toggleSelectAll() {
    const checkboxes = document.querySelectorAll('.article-checkbox');
    const selectAllChecked = document.getElementById('selectAllArticles').checked;

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllChecked;
        const articleId = parseInt(checkbox.value);

        if (selectAllChecked) {
            selectedArticles.add(articleId);
        } else {
            selectedArticles.delete(articleId);
        }
    });

    updateBulkActionsPanel();
}

function updateSelectAllCheckbox() {
    const checkboxes = document.querySelectorAll('.article-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllArticles');
    const selectAllHeader = document.getElementById('selectAllHeader');

    if (checkboxes.length === 0) {
        if (selectAllCheckbox) selectAllCheckbox.checked = false;
        if (selectAllHeader) selectAllHeader.checked = false;
        return;
    }

    const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
    const allChecked = checkedCount === checkboxes.length;
    const someChecked = checkedCount > 0;

    if (selectAllCheckbox) {
        selectAllCheckbox.checked = allChecked;
        selectAllCheckbox.indeterminate = someChecked && !allChecked;
    }

    if (selectAllHeader) {
        selectAllHeader.checked = allChecked;
        selectAllHeader.indeterminate = someChecked && !allChecked;
    }
}

function updateBulkActionsPanel() {
    const panel = document.getElementById('bulkActionsPanel');
    const countSpan = document.getElementById('selectedCount');

    if (!panel || !countSpan) return;

    const count = selectedArticles.size;

    if (count > 0) {
        panel.style.display = 'block';
        countSpan.textContent = count;
    } else {
        panel.style.display = 'none';
    }
}

// Pagination Functions
function updatePagination(pagination) {
    const container = document.getElementById('articlesPagination');
    if (!container || !pagination) return;

    const { current_page, total_pages, has_prev, has_next } = pagination;

    let html = '';

    // Previous button
    html += `<button class="pagination-btn" ${!has_prev ? 'disabled' : ''} onclick="changePage(${current_page - 1})">
        <i class="fas fa-chevron-right"></i> السابق
    </button>`;

    // Page numbers
    const startPage = Math.max(1, current_page - 2);
    const endPage = Math.min(total_pages, current_page + 2);

    if (startPage > 1) {
        html += `<button class="pagination-btn" onclick="changePage(1)">1</button>`;
        if (startPage > 2) {
            html += `<span class="pagination-dots">...</span>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        html += `<button class="pagination-btn ${i === current_page ? 'active' : ''}" onclick="changePage(${i})">${i}</button>`;
    }

    if (endPage < total_pages) {
        if (endPage < total_pages - 1) {
            html += `<span class="pagination-dots">...</span>`;
        }
        html += `<button class="pagination-btn" onclick="changePage(${total_pages})">${total_pages}</button>`;
    }

    // Next button
    html += `<button class="pagination-btn" ${!has_next ? 'disabled' : ''} onclick="changePage(${current_page + 1})">
        التالي <i class="fas fa-chevron-left"></i>
    </button>`;

    container.innerHTML = html;
}

function changePage(page) {
    currentPage = page;
    loadArticles();
}

function changeArticlesPerPage() {
    const select = document.getElementById('articlesPerPage');
    if (select) {
        articlesPerPage = parseInt(select.value);
        currentPage = 1; // Reset to first page
        loadArticles();
    }
}

// Update Articles Stats
function updateArticlesStats(stats) {
    if (!stats) return;

    const elements = {
        totalArticles: document.getElementById('totalArticles'),
        publishedArticles: document.getElementById('publishedArticles'),
        draftArticles: document.getElementById('draftArticles'),
        totalViews: document.getElementById('totalViews')
    };

    if (elements.totalArticles) elements.totalArticles.textContent = formatNumber(stats.total || 0);
    if (elements.publishedArticles) elements.publishedArticles.textContent = formatNumber(stats.published || 0);
    if (elements.draftArticles) elements.draftArticles.textContent = formatNumber(stats.draft || 0);
    if (elements.totalViews) elements.totalViews.textContent = formatNumber(stats.total_views || 0);
}

// Update Table Info
function updateTableInfo(pagination) {
    const infoElement = document.getElementById('tableInfo');
    if (!infoElement || !pagination) return;

    const { current_page, per_page, total_items, from, to } = pagination;

    if (total_items === 0) {
        infoElement.textContent = 'لا توجد مقالات';
    } else {
        infoElement.textContent = `عرض ${from} إلى ${to} من ${formatNumber(total_items)} مقال`;
    }
}

// Advanced Articles Functions
function sortArticles(field) {
    if (currentSort.field === field) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.field = field;
        currentSort.direction = 'asc';
    }

    // Update sort indicators
    document.querySelectorAll('.sortable').forEach(th => {
        th.classList.remove('asc', 'desc');
    });

    const currentTh = document.querySelector(`[onclick="sortArticles('${field}')"]`);
    if (currentTh) {
        currentTh.classList.add(currentSort.direction);
    }

    currentPage = 1; // Reset to first page
    loadArticles();
}

function clearFilters() {
    // Clear all filter inputs
    const filters = [
        'articleSearch', 'categoryFilter', 'statusFilter',
        'authorFilter', 'dateFromFilter', 'dateToFilter', 'viewsFilter'
    ];

    filters.forEach(filterId => {
        const element = document.getElementById(filterId);
        if (element) {
            element.value = '';
        }
    });

    // Reset pagination and sorting
    currentPage = 1;
    currentSort = { field: 'created_at', direction: 'desc' };

    // Clear selected articles
    selectedArticles.clear();
    updateBulkActionsPanel();

    // Reload articles
    loadArticles();
}

function exportArticles() {
    const params = new URLSearchParams({
        action: 'export_articles',
        format: 'csv'
    });

    // Add current filters
    const search = document.getElementById('articleSearch')?.value || '';
    const category = document.getElementById('categoryFilter')?.value || '';
    const status = document.getElementById('statusFilter')?.value || '';

    if (search) params.append('search', search);
    if (category) params.append('category', category);
    if (status) params.append('status', status);

    // Create download link
    const url = `php/dashboard-actions.php?${params}`;
    const link = document.createElement('a');
    link.href = url;
    link.download = `articles_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast('تم بدء تصدير المقالات', 'success');
}

// Bulk Actions Functions
function bulkActions() {
    const panel = document.getElementById('bulkActionsPanel');
    if (panel) {
        panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
    }
}

function bulkChangeStatus(newStatus) {
    if (selectedArticles.size === 0) {
        showToast('يرجى تحديد مقالات أولاً', 'warning');
        return;
    }

    const articleIds = Array.from(selectedArticles);
    const formData = new FormData();
    formData.append('action', 'bulk_change_status');
    formData.append('article_ids', JSON.stringify(articleIds));
    formData.append('status', newStatus);

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast(`تم تغيير حالة ${articleIds.length} مقال بنجاح`, 'success');
            selectedArticles.clear();
            updateBulkActionsPanel();
            loadArticles();
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

function bulkChangeCategory() {
    if (selectedArticles.size === 0) {
        showToast('يرجى تحديد مقالات أولاً', 'warning');
        return;
    }

    // Show category selection modal
    const categoryId = prompt('أدخل معرف التصنيف الجديد:');
    if (!categoryId) return;

    const articleIds = Array.from(selectedArticles);
    const formData = new FormData();
    formData.append('action', 'bulk_change_category');
    formData.append('article_ids', JSON.stringify(articleIds));
    formData.append('category_id', categoryId);

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast(`تم تغيير تصنيف ${articleIds.length} مقال بنجاح`, 'success');
            selectedArticles.clear();
            updateBulkActionsPanel();
            loadArticles();
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

function bulkDelete() {
    if (selectedArticles.size === 0) {
        showToast('يرجى تحديد مقالات أولاً', 'warning');
        return;
    }

    if (!confirm(`هل أنت متأكد من حذف ${selectedArticles.size} مقال؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    const articleIds = Array.from(selectedArticles);
    const formData = new FormData();
    formData.append('action', 'bulk_delete');
    formData.append('article_ids', JSON.stringify(articleIds));

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast(`تم حذف ${articleIds.length} مقال بنجاح`, 'success');
            selectedArticles.clear();
            updateBulkActionsPanel();
            loadArticles();
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

function cancelBulkActions() {
    selectedArticles.clear();
    updateBulkActionsPanel();

    // Uncheck all checkboxes
    document.querySelectorAll('.article-checkbox').forEach(cb => {
        cb.checked = false;
    });
    updateSelectAllCheckbox();
}

// Article Actions
function viewArticle(id) {
    // Open article in new tab/window
    window.open(`article.php?id=${id}`, '_blank');
}

function duplicateArticle(id) {
    if (!confirm('هل تريد إنشاء نسخة من هذا المقال؟')) {
        return;
    }

    const formData = new FormData();
    formData.append('action', 'duplicate_article');
    formData.append('id', id);

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast('تم إنشاء نسخة من المقال بنجاح', 'success');
            loadArticles();
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

// Search and Filter Functions
function setupSearchAndFilters() {
    const articleSearch = document.getElementById('articleSearch');
    if (articleSearch) {
        articleSearch.addEventListener('input', debounce(loadArticles, 500));
    }
}

function filterArticles() {
    currentPage = 1; // Reset to first page when filtering
    loadArticles();
}

// Debounce function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Load Categories Grid
function loadCategoriesGrid() {
    fetch('php/dashboard-data.php?action=categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayCategoriesGrid(data.categories);
            }
        })
        .catch(error => console.error('Error loading categories:', error));
}

// Display Categories Grid
function displayCategoriesGrid(categories) {
    const container = document.getElementById('categoriesGrid');
    if (!container) return;

    if (categories.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6b7280; grid-column: 1/-1;">لا توجد تصنيفات</p>';
        return;
    }

    const html = categories.map(category => `
        <div class="category-card">
            <div class="category-header">
                <div class="category-icon">
                    <i class="fas fa-tag"></i>
                </div>
                <div class="category-info">
                    <h3>${category.name}</h3>
                    <p>${category.description || 'لا يوجد وصف'}</p>
                </div>
            </div>
            <div class="category-stats">
                <span class="category-count">${category.articles_count || 0} مقال</span>
                <div class="category-actions">
                    <button class="action-btn edit" onclick="editCategory(${category.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete" onclick="deleteCategory(${category.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Load Doctors
function loadDoctors() {
    fetch('php/dashboard-data.php?action=doctors')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayDoctorsGrid(data.doctors);
            }
        })
        .catch(error => console.error('Error loading doctors:', error));
}

// Display Doctors Grid
function displayDoctorsGrid(doctors) {
    const container = document.getElementById('doctorsGrid');
    if (!container) return;

    if (doctors.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6b7280; grid-column: 1/-1;">لا توجد أطباء</p>';
        return;
    }

    const html = doctors.map(doctor => `
        <div class="team-card">
            <div class="team-member-image">
                <i class="fas fa-user-md"></i>
            </div>
            <div class="team-member-info">
                <h4>${doctor.name}</h4>
                <div class="specialty">${doctor.specialty}</div>
                <div class="experience">${doctor.experience} سنة خبرة</div>
                ${doctor.phone ? `<div style="color: #6b7280; font-size: 0.9rem; margin-bottom: 0.5rem;"><i class="fas fa-phone" style="margin-left: 0.5rem;"></i>${doctor.phone}</div>` : ''}
                ${doctor.bio ? `<p style="color: #6b7280; font-size: 0.9rem; line-height: 1.4;">${doctor.bio}</p>` : ''}
            </div>
            <div class="team-actions">
                <button class="btn btn-sm btn-secondary" onclick="editDoctor(${doctor.id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteDoctor(${doctor.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Load Services
function loadServices() {
    fetch('php/dashboard-data.php?action=services')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayServicesGrid(data.services);
            }
        })
        .catch(error => console.error('Error loading services:', error));
}

// Display Services Grid
function displayServicesGrid(services) {
    const container = document.getElementById('servicesGrid');
    if (!container) return;

    if (services.length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #6b7280; grid-column: 1/-1;">لا توجد خدمات</p>';
        return;
    }

    const html = services.map(service => `
        <div class="service-management-card">
            <div class="service-card-header">
                <div class="service-icon">
                    <i class="${service.icon || 'fas fa-tooth'}"></i>
                </div>
                <div class="service-status active">نشط</div>
            </div>
            <div class="service-card-body">
                <h3>${service.name}</h3>
                <div class="service-category">
                    <i class="fas fa-tag"></i>
                    <span>${service.category}</span>
                </div>
                <div class="service-price">
                    <i class="fas fa-money-bill-wave"></i>
                    <span>${formatNumber(service.price)} ريال</span>
                </div>
                ${service.duration ? `<div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem; color: #6b7280; font-size: 0.95rem;"><i class="fas fa-clock" style="color: #d4af37; width: 16px;"></i><span>${service.duration}</span></div>` : ''}
                ${service.description ? `<p style="color: #6b7280; font-size: 0.9rem; line-height: 1.4; margin-top: 1rem;">${service.description}</p>` : ''}
            </div>
            <div class="service-card-actions">
                <button class="btn btn-secondary btn-sm" onclick="editService(${service.id})">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger btn-sm" onclick="deleteService(${service.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');

    container.innerHTML = html;
}

// Initialize Charts
function initializeCharts() {
    initializeDashboardQuickCharts();
}

// Initialize Dashboard Quick Charts (for main dashboard)
function initializeDashboardQuickCharts() {
    initializeDashboardCategoriesChart();
    initializeDashboardViewsChart();
}

// Initialize Dashboard Categories Chart (simplified)
function initializeDashboardCategoriesChart() {
    fetch('php/dashboard-data.php?action=categories_chart')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createDashboardCategoriesChart(data.chart_data);
            }
        })
        .catch(error => console.error('Error loading dashboard categories chart:', error));
}

function createDashboardCategoriesChart(chartData) {
    const ctx = document.getElementById('dashboardCategoriesChart');
    if (!ctx) return;

    // تدمير الرسم البياني السابق إذا كان موجوداً
    if (window.dashboardCategoriesChartInstance) {
        window.dashboardCategoriesChartInstance.destroy();
    }

    window.dashboardCategoriesChartInstance = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels.slice(0, 5), // Show only top 5
            datasets: [{
                data: chartData.data.slice(0, 5),
                backgroundColor: [
                    '#d4af37',
                    '#f4d03f',
                    '#f7dc6f',
                    '#f8c471',
                    '#f5b041'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            resizeDelay: 0,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo',
                            size: 10
                        },
                        padding: 8,
                        usePointStyle: true
                    }
                }
            },
            layout: {
                padding: 10
            }
        }
    });
}

// Initialize Dashboard Views Chart (simplified)
function initializeDashboardViewsChart() {
    fetch('php/dashboard-data.php?action=dashboard_views_trend')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createDashboardViewsChart(data.chart_data);
            }
        })
        .catch(error => {
            // Fallback to regular views chart if dashboard-specific one doesn't exist
            fetch('php/dashboard-data.php?action=views_chart')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        createDashboardViewsChart(data.chart_data);
                    }
                })
                .catch(error => console.error('Error loading dashboard views chart:', error));
        });
}

function createDashboardViewsChart(chartData) {
    const ctx = document.getElementById('dashboardViewsChart');
    if (!ctx) return;

    // تدمير الرسم البياني السابق إذا كان موجوداً
    if (window.dashboardViewsChartInstance) {
        window.dashboardViewsChartInstance.destroy();
    }

    window.dashboardViewsChartInstance = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels.slice(-7), // Last 7 days only
            datasets: [{
                label: 'المشاهدات',
                data: chartData.data.slice(-7),
                borderColor: '#d4af37',
                backgroundColor: 'rgba(212, 175, 55, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 3,
                pointHoverRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            resizeDelay: 0,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    display: false
                },
                x: {
                    display: false
                }
            },
            elements: {
                point: {
                    backgroundColor: '#d4af37'
                }
            },
            layout: {
                padding: 10
            }
        }
    });
}

// Initialize Categories Chart
function initializeCategoriesChart() {
    fetch('php/dashboard-data.php?action=categories_chart')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createCategoriesChart(data.chart_data);
            }
        })
        .catch(error => console.error('Error loading categories chart:', error));
}

// Create Categories Chart
function createCategoriesChart(chartData) {
    const ctx = document.getElementById('categoriesChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.data,
                backgroundColor: [
                    '#d4af37',
                    '#daa520',
                    '#ffd700',
                    '#f4a460',
                    '#cd853f',
                    '#b8860b'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            }
        }
    });
}

// Initialize Views Chart
function initializeViewsChart() {
    fetch('php/dashboard-data.php?action=views_chart')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createViewsChart(data.chart_data);
            }
        })
        .catch(error => console.error('Error loading views chart:', error));
}

// Create Views Chart
function createViewsChart(chartData) {
    const ctx = document.getElementById('viewsChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'المشاهدات',
                data: chartData.data,
                borderColor: '#d4af37',
                backgroundColor: 'rgba(212, 175, 55, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
}

// Edit and Delete Functions
function editArticle(id) {
    // جلب بيانات المقال
    fetch(`php/dashboard-data.php?action=get_article&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateArticleForm(data.article);
                openModal('articleModal');
            } else {
                showToast('خطأ في جلب بيانات المقال: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

function deleteArticle(id) {
    if (confirm('هل أنت متأكد من حذف هذا المقال؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_article');
        formData.append('id', id);

        showLoading();

        fetch('php/dashboard-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showToast('تم حذف المقال بنجاح', 'success');
                loadArticles();
            } else {
                showToast('حدث خطأ: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('حدث خطأ في الاتصال', 'error');
        });
    }
}

function editCategory(id) {
    // جلب بيانات التصنيف
    fetch(`php/dashboard-data.php?action=get_category&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateCategoryForm(data.category);
                openModal('categoryModal');
            } else {
                showToast('خطأ في جلب بيانات التصنيف: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

function deleteCategory(id) {
    if (confirm('هل أنت متأكد من حذف هذا التصنيف؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_category');
        formData.append('id', id);

        showLoading();

        fetch('php/dashboard-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showToast('تم حذف التصنيف بنجاح', 'success');
                loadCategoriesGrid();
                loadCategories(); // Reload for dropdowns
            } else {
                showToast('حدث خطأ: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('حدث خطأ في الاتصال', 'error');
        });
    }
}

function editDoctor(id) {
    // جلب بيانات الطبيب
    fetch(`php/dashboard-data.php?action=get_doctor&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateDoctorForm(data.doctor);
                openModal('doctorModal');
            } else {
                showToast('خطأ في جلب بيانات الطبيب: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

function deleteDoctor(id) {
    if (confirm('هل أنت متأكد من حذف هذا الطبيب؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_doctor');
        formData.append('id', id);

        showLoading();

        fetch('php/dashboard-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showToast('تم حذف الطبيب بنجاح', 'success');
                loadDoctors();
            } else {
                showToast('حدث خطأ: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('حدث خطأ في الاتصال', 'error');
        });
    }
}

function editService(id) {
    // جلب بيانات الخدمة
    fetch(`php/dashboard-data.php?action=get_service&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateServiceForm(data.service);
                openModal('serviceModal');
            } else {
                showToast('خطأ في جلب بيانات الخدمة: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('حدث خطأ في الاتصال', 'error');
        });
}

function deleteService(id) {
    if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
        const formData = new FormData();
        formData.append('action', 'delete_service');
        formData.append('id', id);

        showLoading();

        fetch('php/dashboard-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showToast('تم حذف الخدمة بنجاح', 'success');
                loadServices();
            } else {
                showToast('حدث خطأ: ' + data.message, 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showToast('حدث خطأ في الاتصال', 'error');
        });
    }
}

// Load Analytics
function loadAnalytics() {
    console.log('Loading analytics...');
    showLoading();

    // Load analytics data with error handling
    Promise.all([
        loadAnalyticsSummary(),
        loadTopArticlesChart(),
        loadContentGrowthChart(),
        loadCategoriesDistributionChart(),
        loadDailyViewsChart(),
        loadTopArticlesTable(),
        loadCategoriesStatsTable()
    ]).then(() => {
        hideLoading();
        console.log('Analytics loaded successfully');
    }).catch(error => {
        hideLoading();
        console.error('Error loading analytics:', error);
        showToast('حدث خطأ في تحميل التحليلات', 'error');
    });
}

// Load Analytics Summary
function loadAnalyticsSummary() {
    return fetch('php/dashboard-data.php?action=analytics_summary')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAnalyticsSummary(data.summary);
            } else {
                console.error('Failed to load analytics summary:', data.message);
            }
        })
        .catch(error => {
            console.error('Error loading analytics summary:', error);
            // Show fallback data
            updateAnalyticsSummary({
                total_views: 0,
                total_articles: 0,
                avg_views: 0,
                top_category: { name: 'غير محدد', count: 0 }
            });
        });
}

function updateAnalyticsSummary(summary) {
    // Update total page views
    const totalViewsEl = document.getElementById('totalPageViews');
    if (totalViewsEl) {
        totalViewsEl.textContent = formatNumber(summary.total_views || 0);
    }

    // Update total articles
    const totalArticlesEl = document.getElementById('totalArticles');
    if (totalArticlesEl) {
        totalArticlesEl.textContent = formatNumber(summary.total_articles || 0);
    }

    // Update average views
    const avgViewsEl = document.getElementById('avgViews');
    if (avgViewsEl) {
        const avg = summary.total_articles > 0 ? Math.round(summary.total_views / summary.total_articles) : 0;
        avgViewsEl.textContent = formatNumber(avg);
    }

    // Update top category
    const topCategoryEl = document.getElementById('topCategory');
    const topCategoryCountEl = document.getElementById('topCategoryCount');
    if (topCategoryEl && summary.top_category) {
        topCategoryEl.textContent = summary.top_category.name;
        if (topCategoryCountEl) {
            topCategoryCountEl.textContent = `${summary.top_category.count} مقال`;
        }
    }

    // Update trends (you can calculate these based on historical data)
    updateTrends(summary);
}

function updateTrends(summary) {
    // This would typically compare with previous period data
    // For now, we'll show static positive trends
    const viewsTrendEl = document.getElementById('viewsTrend');
    const articlesTrendEl = document.getElementById('articlesTrend');

    if (viewsTrendEl) {
        viewsTrendEl.textContent = '+12% من الشهر الماضي';
        viewsTrendEl.className = 'trend positive';
    }

    if (articlesTrendEl) {
        articlesTrendEl.textContent = `+${summary.recent_articles || 5} مقالات جديدة`;
        articlesTrendEl.className = 'trend positive';
    }
}

// Load Top Articles Chart
function loadTopArticlesChart() {
    return fetch('php/dashboard-data.php?action=top_articles')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createTopArticlesChart(data.chart_data);
            } else {
                console.error('Failed to load top articles:', data.message);
                // Show fallback chart
                createTopArticlesChart({
                    labels: ['لا توجد بيانات'],
                    data: [0]
                });
            }
        })
        .catch(error => {
            console.error('Error loading top articles:', error);
            createTopArticlesChart({
                labels: ['خطأ في التحميل'],
                data: [0]
            });
        });
}

// Load Content Growth Chart
function loadContentGrowthChart() {
    return fetch('php/dashboard-data.php?action=content_growth')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createContentGrowthChart(data.chart_data);
            } else {
                console.error('Failed to load content growth:', data.message);
                createContentGrowthChart({
                    labels: ['لا توجد بيانات'],
                    data: [0]
                });
            }
        })
        .catch(error => {
            console.error('Error loading content growth:', error);
            createContentGrowthChart({
                labels: ['خطأ في التحميل'],
                data: [0]
            });
        });
}

// Create Top Articles Chart
function createTopArticlesChart(chartData) {
    const ctx = document.getElementById('topArticlesChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'المشاهدات',
                data: chartData.data,
                backgroundColor: 'rgba(212, 175, 55, 0.8)',
                borderColor: '#d4af37',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        },
                        maxRotation: 45
                    }
                }
            }
        }
    });
}

// Create Content Growth Chart
function createContentGrowthChart(chartData) {
    const ctx = document.getElementById('contentGrowthChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'المقالات الجديدة',
                data: chartData.data,
                borderColor: '#d4af37',
                backgroundColor: 'rgba(212, 175, 55, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
}

// Form Population Functions
function populateArticleForm(article) {
    const form = document.getElementById('articleForm');
    if (!form) return;

    // إضافة معرف المقال للتعديل
    let idInput = form.querySelector('input[name="id"]');
    if (!idInput) {
        idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        form.appendChild(idInput);
    }
    idInput.value = article.id;

    // ملء الحقول
    form.querySelector('input[name="title"]').value = article.title;
    form.querySelector('textarea[name="content"]').value = article.content;
    form.querySelector('select[name="category_id"]').value = article.category_id;
    form.querySelector('select[name="status"]').value = article.status;

    // تغيير عنوان النافذة
    const modalTitle = document.querySelector('#articleModal .modal-header h2');
    if (modalTitle) {
        modalTitle.textContent = 'تعديل المقال';
    }

    // تغيير نص الزر
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث المقال';
    }
}

function populateCategoryForm(category) {
    const form = document.getElementById('categoryForm');
    if (!form) return;

    // إضافة معرف التصنيف للتعديل
    let idInput = form.querySelector('input[name="id"]');
    if (!idInput) {
        idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        form.appendChild(idInput);
    }
    idInput.value = category.id;

    // ملء الحقول
    form.querySelector('input[name="name"]').value = category.name;
    form.querySelector('textarea[name="description"]').value = category.description || '';

    // تغيير عنوان النافذة
    const modalTitle = document.querySelector('#categoryModal .modal-header h2');
    if (modalTitle) {
        modalTitle.textContent = 'تعديل التصنيف';
    }

    // تغيير نص الزر
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث التصنيف';
    }
}

function populateDoctorForm(doctor) {
    const form = document.getElementById('doctorForm');
    if (!form) return;

    // إضافة معرف الطبيب للتعديل
    let idInput = form.querySelector('input[name="id"]');
    if (!idInput) {
        idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        form.appendChild(idInput);
    }
    idInput.value = doctor.id;

    // ملء الحقول
    form.querySelector('input[name="name"]').value = doctor.name;
    form.querySelector('input[name="specialty"]').value = doctor.specialty;
    form.querySelector('input[name="experience"]').value = doctor.experience;
    form.querySelector('input[name="phone"]').value = doctor.phone || '';
    form.querySelector('textarea[name="bio"]').value = doctor.bio || '';

    // تغيير عنوان النافذة
    const modalTitle = document.querySelector('#doctorModal .modal-header h2');
    if (modalTitle) {
        modalTitle.textContent = 'تعديل بيانات الطبيب';
    }

    // تغيير نص الزر
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث البيانات';
    }
}

function populateServiceForm(service) {
    const form = document.getElementById('serviceForm');
    if (!form) return;

    // إضافة معرف الخدمة للتعديل
    let idInput = form.querySelector('input[name="id"]');
    if (!idInput) {
        idInput = document.createElement('input');
        idInput.type = 'hidden';
        idInput.name = 'id';
        form.appendChild(idInput);
    }
    idInput.value = service.id;

    // ملء الحقول
    form.querySelector('input[name="name"]').value = service.name;
    form.querySelector('input[name="category"]').value = service.category;
    form.querySelector('input[name="price"]').value = service.price;
    form.querySelector('input[name="duration"]').value = service.duration || '';
    form.querySelector('textarea[name="description"]').value = service.description || '';
    form.querySelector('input[name="icon"]').value = service.icon || 'fas fa-tooth';

    // تغيير عنوان النافذة
    const modalTitle = document.querySelector('#serviceModal .modal-header h2');
    if (modalTitle) {
        modalTitle.textContent = 'تعديل الخدمة';
    }

    // تغيير نص الزر
    const submitBtn = form.querySelector('button[type="submit"]');
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-save"></i> تحديث الخدمة';
    }
}

// Reset Form Function
function resetFormForAdd(formId, modalId) {
    const form = document.getElementById(formId);
    if (!form) return;

    // إزالة معرف التعديل
    const idInput = form.querySelector('input[name="id"]');
    if (idInput) {
        idInput.remove();
    }

    // إعادة تعيين النصوص
    const modalTitle = document.querySelector(`#${modalId} .modal-header h2`);
    const submitBtn = form.querySelector('button[type="submit"]');

    switch(formId) {
        case 'articleForm':
            if (modalTitle) modalTitle.textContent = 'إضافة مقال جديد';
            if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-plus"></i> حفظ المقال';
            break;
        case 'categoryForm':
            if (modalTitle) modalTitle.textContent = 'إضافة تصنيف جديد';
            if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-plus"></i> حفظ التصنيف';
            break;
        case 'doctorForm':
            if (modalTitle) modalTitle.textContent = 'إضافة طبيب جديد';
            if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-plus"></i> حفظ بيانات الطبيب';
            break;
        case 'serviceForm':
            if (modalTitle) modalTitle.textContent = 'إضافة خدمة جديدة';
            if (submitBtn) submitBtn.innerHTML = '<i class="fas fa-plus"></i> حفظ الخدمة';
            break;
    }
}

// تحديث وظائف معالجة النماذج لدعم التعديل
function handleArticleSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);

    // تحديد نوع العملية (إضافة أو تعديل)
    const isEdit = formData.has('id') && formData.get('id');
    formData.append('action', isEdit ? 'edit_article' : 'add_article');

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast(data.message, 'success');
            closeModal('articleModal');
            loadArticles();
            loadDashboardData(); // تحديث الإحصائيات
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

// دالة معاينة الصور
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);

    if (input.files && input.files[0]) {
        const file = input.files[0];

        // التحقق من نوع الملف
        if (!file.type.startsWith('image/')) {
            showToast('يرجى اختيار ملف صورة صحيح', 'error');
            input.value = '';
            return;
        }

        // التحقق من حجم الملف (5MB)
        if (file.size > 5 * 1024 * 1024) {
            showToast('حجم الصورة يجب أن يكون أقل من 5MB', 'error');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `
                <img src="${e.target.result}" style="max-width: 100%; max-height: 200px; border-radius: 8px;">
                <p style="margin-top: 10px; color: #666;">${file.name}</p>
                <button type="button" onclick="removeImage('${input.name}', '${previewId}')"
                        style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">
                    <i class="fas fa-trash"></i> إزالة
                </button>
            `;
        };
        reader.readAsDataURL(file);
    }
}

// دالة إزالة الصورة
function removeImage(inputName, previewId) {
    const input = document.querySelector(`input[name="${inputName}"]`);
    const preview = document.getElementById(previewId);

    input.value = '';
    preview.innerHTML = `
        <i class="fas fa-cloud-upload-alt"></i>
        <p>اسحب الصورة هنا أو انقر للاختيار</p>
        <small>الحد الأقصى: 5MB - الصيغ المدعومة: JPG, PNG, GIF</small>
    `;
}

function handleCategorySubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);

    // تحديد نوع العملية (إضافة أو تعديل)
    const isEdit = formData.has('id') && formData.get('id');
    formData.append('action', isEdit ? 'edit_category' : 'add_category');

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast(data.message, 'success');
            closeModal('categoryModal');
            loadCategoriesGrid();
            loadCategories(); // Reload for dropdowns
            loadDashboardData(); // تحديث الإحصائيات
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

function handleDoctorSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);

    // تحديد نوع العملية (إضافة أو تعديل)
    const isEdit = formData.has('id') && formData.get('id');
    formData.append('action', isEdit ? 'edit_doctor' : 'add_doctor');

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast(data.message, 'success');
            closeModal('doctorModal');
            loadDoctors();
            loadDashboardData(); // تحديث الإحصائيات
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

function handleServiceSubmit(e) {
    e.preventDefault();
    const formData = new FormData(e.target);

    // تحديد نوع العملية (إضافة أو تعديل)
    const isEdit = formData.has('id') && formData.get('id');
    formData.append('action', isEdit ? 'edit_service' : 'add_service');

    showLoading();

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoading();
        if (data.success) {
            showToast(data.message, 'success');
            closeModal('serviceModal');
            loadServices();
            loadDashboardData(); // تحديث الإحصائيات
        } else {
            showToast('حدث خطأ: ' + data.message, 'error');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('حدث خطأ في الاتصال', 'error');
    });
}

// تحديث وظائف فتح النوافذ المنبثقة
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        // إعادة تعيين النموذج للإضافة إذا لم يكن يحتوي على معرف
        const form = modal.querySelector('form');
        if (form && !form.querySelector('input[name="id"]')) {
            resetFormForAdd(form.id, modalId);
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
    }
}

// وظيفة البحث العام
function globalSearch(query) {
    if (!query || query.length < 2) {
        return;
    }

    fetch(`php/dashboard-data.php?action=search_data&query=${encodeURIComponent(query)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displaySearchResults(data.results);
            }
        })
        .catch(error => {
            console.error('خطأ في البحث:', error);
        });
}

// عرض نتائج البحث
function displaySearchResults(results) {
    const container = document.getElementById('searchResults');
    if (!container) return;

    if (results.length === 0) {
        container.innerHTML = '<div class="search-no-results">لا توجد نتائج</div>';
        container.style.display = 'block';
        return;
    }

    const html = results.map(result => {
        const typeText = getTypeText(result.type);
        const icon = getTypeIcon(result.type);

        return `
            <div class="search-result-item" onclick="goToResult('${result.type}', ${result.id})">
                <div class="result-icon">
                    <i class="${icon}"></i>
                </div>
                <div class="result-content">
                    <div class="result-title">${result.title}</div>
                    <div class="result-type">${typeText}</div>
                </div>
            </div>
        `;
    }).join('');

    container.innerHTML = html;
    container.style.display = 'block';
}

// الحصول على نص نوع النتيجة
function getTypeText(type) {
    switch(type) {
        case 'article': return 'مقال';
        case 'doctor': return 'طبيب';
        case 'service': return 'خدمة';
        default: return 'غير معروف';
    }
}

// الحصول على أيقونة نوع النتيجة
function getTypeIcon(type) {
    switch(type) {
        case 'article': return 'fas fa-newspaper';
        case 'doctor': return 'fas fa-user-md';
        case 'service': return 'fas fa-cogs';
        default: return 'fas fa-question';
    }
}

// الانتقال إلى النتيجة
function goToResult(type, id) {
    // إخفاء نتائج البحث
    const container = document.getElementById('searchResults');
    if (container) {
        container.style.display = 'none';
    }

    // الانتقال إلى القسم المناسب
    switch(type) {
        case 'article':
            showSection('articles');
            // يمكن إضافة تمييز للمقال المحدد
            break;
        case 'doctor':
            showSection('doctors');
            break;
        case 'service':
            showSection('services');
            break;
    }
}

// تنفيذ البحث العام
function performGlobalSearch() {
    const input = document.getElementById('globalSearchInput');
    if (!input) return;

    const query = input.value.trim();
    if (query.length < 2) {
        showToast('يجب إدخال حرفين على الأقل للبحث', 'warning');
        return;
    }

    globalSearch(query);
}

// إضافة مستمع للبحث عند الكتابة
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('globalSearchInput');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(function() {
            const query = this.value.trim();
            if (query.length >= 2) {
                globalSearch(query);
            } else {
                const container = document.getElementById('searchResults');
                if (container) {
                    container.style.display = 'none';
                }
            }
        }, 500));

        // إخفاء النتائج عند النقر خارجها
        document.addEventListener('click', function(e) {
            const container = document.getElementById('searchResults');
            const searchContainer = document.querySelector('.global-search');

            if (container && searchContainer && !searchContainer.contains(e.target)) {
                container.style.display = 'none';
            }
        });
    }
});

// تحديث الإحصائيات المتقدمة
function loadAdvancedStats() {
    fetch('php/dashboard-data.php?action=dashboard_summary')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAdvancedStatsDisplay(data.summary);
            }
        })
        .catch(error => {
            console.error('خطأ في جلب الإحصائيات المتقدمة:', error);
        });
}

// عرض الإحصائيات المتقدمة
function updateAdvancedStatsDisplay(summary) {
    // يمكن إضافة عناصر جديدة في لوحة التحكم لعرض هذه الإحصائيات
    console.log('الإحصائيات المتقدمة:', summary);
}

// Form Tabs Functions
function switchTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // Remove active class from all tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab content
    const targetTab = document.getElementById(`${tabName}-tab`);
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // Add active class to clicked button
    const targetBtn = document.querySelector(`[onclick="switchTab('${tabName}')"]`);
    if (targetBtn) {
        targetBtn.classList.add('active');
    }
}

// Text Editor Functions
function formatText(command) {
    const textarea = document.querySelector('#content-tab textarea[name="content"]');
    if (!textarea) return;

    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);

    let formattedText = '';

    switch(command) {
        case 'bold':
            formattedText = `**${selectedText}**`;
            break;
        case 'italic':
            formattedText = `*${selectedText}*`;
            break;
        case 'underline':
            formattedText = `<u>${selectedText}</u>`;
            break;
        default:
            formattedText = selectedText;
    }

    textarea.value = textarea.value.substring(0, start) + formattedText + textarea.value.substring(end);
    textarea.focus();
    textarea.setSelectionRange(start + formattedText.length, start + formattedText.length);

    updateContentStats();
}

function insertList(type) {
    const textarea = document.querySelector('#content-tab textarea[name="content"]');
    if (!textarea) return;

    const start = textarea.selectionStart;
    const listItem = type === 'ul' ? '- عنصر القائمة\n' : '1. عنصر القائمة\n';

    textarea.value = textarea.value.substring(0, start) + listItem + textarea.value.substring(start);
    textarea.focus();
    textarea.setSelectionRange(start + listItem.length, start + listItem.length);

    updateContentStats();
}

function insertLink() {
    const textarea = document.querySelector('#content-tab textarea[name="content"]');
    if (!textarea) return;

    const url = prompt('أدخل الرابط:');
    if (!url) return;

    const text = prompt('أدخل نص الرابط:') || url;
    const link = `[${text}](${url})`;

    const start = textarea.selectionStart;
    textarea.value = textarea.value.substring(0, start) + link + textarea.value.substring(start);
    textarea.focus();
    textarea.setSelectionRange(start + link.length, start + link.length);

    updateContentStats();
}

function updateContentStats() {
    const textarea = document.querySelector('#content-tab textarea[name="content"]');
    const wordCountEl = document.getElementById('wordCount');
    const charCountEl = document.getElementById('charCount');

    if (!textarea || !wordCountEl || !charCountEl) return;

    const content = textarea.value;
    const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
    const charCount = content.length;

    wordCountEl.textContent = `${wordCount} كلمة`;
    charCountEl.textContent = `${charCount} حرف`;
}

// Article Form Functions
function saveAsDraft() {
    const form = document.getElementById('articleForm');
    if (!form) return;

    // Set status to draft
    const statusSelect = form.querySelector('select[name="status"]');
    if (statusSelect) {
        statusSelect.value = 'draft';
    }

    // Submit form
    form.dispatchEvent(new Event('submit'));
}

function previewArticle() {
    const form = document.getElementById('articleForm');
    if (!form) return;

    const formData = new FormData(form);
    formData.append('action', 'preview_article');

    // Open preview in new window
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write('<html><head><title>معاينة المقال</title></head><body><h1>جاري التحميل...</h1></body></html>');

    fetch('php/dashboard-actions.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(html => {
        previewWindow.document.open();
        previewWindow.document.write(html);
        previewWindow.document.close();
    })
    .catch(error => {
        previewWindow.document.write('<h1>خطأ في تحميل المعاينة</h1>');
        console.error('Preview error:', error);
    });
}

// Load Authors for Filter
function loadAuthors() {
    fetch('php/dashboard-data.php?action=authors')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAuthorDropdown(data.authors);
            }
        })
        .catch(error => console.error('Error loading authors:', error));
}

function updateAuthorDropdown(authors) {
    const dropdown = document.getElementById('authorFilter');
    if (!dropdown) return;

    const currentValue = dropdown.value;
    dropdown.innerHTML = '<option value="">جميع الكتاب</option>';

    authors.forEach(author => {
        const option = document.createElement('option');
        option.value = author.id;
        option.textContent = author.name;
        if (author.id == currentValue) {
            option.selected = true;
        }
        dropdown.appendChild(option);
    });
}

// Load Categories Distribution Chart
function loadCategoriesDistributionChart() {
    return fetch('php/dashboard-data.php?action=categories_chart')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createCategoriesDistributionChart(data.chart_data);
            } else {
                console.error('Failed to load categories distribution:', data.message);
                createCategoriesDistributionChart({
                    labels: ['لا توجد بيانات'],
                    data: [0]
                });
            }
        })
        .catch(error => {
            console.error('Error loading categories distribution:', error);
            createCategoriesDistributionChart({
                labels: ['خطأ في التحميل'],
                data: [0]
            });
        });
}

function createCategoriesDistributionChart(chartData) {
    const ctx = document.getElementById('categoriesDistributionChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.data,
                backgroundColor: [
                    '#d4af37',
                    '#f4d03f',
                    '#f7dc6f',
                    '#f8c471',
                    '#f5b041',
                    '#eb984e'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        font: {
                            family: 'Cairo'
                        },
                        padding: 15
                    }
                }
            }
        }
    });
}

// Load Daily Views Chart
function loadDailyViewsChart() {
    return fetch('php/dashboard-data.php?action=daily_views')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                createDailyViewsChart(data.chart_data);
            } else {
                console.error('Failed to load daily views:', data.message);
                createDailyViewsChart({
                    labels: ['لا توجد بيانات'],
                    data: [0]
                });
            }
        })
        .catch(error => {
            console.error('Error loading daily views:', error);
            createDailyViewsChart({
                labels: ['خطأ في التحميل'],
                data: [0]
            });
        });
}

function createDailyViewsChart(chartData) {
    const ctx = document.getElementById('dailyViewsChart');
    if (!ctx) return;

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'المشاهدات اليومية',
                data: chartData.data,
                borderColor: '#d4af37',
                backgroundColor: 'rgba(212, 175, 55, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });
}

// Load Top Articles Table
function loadTopArticlesTable() {
    return fetch('php/dashboard-data.php?action=top_articles_table')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayTopArticlesTable(data.articles);
            } else {
                console.error('Failed to load top articles table:', data.message);
                displayTopArticlesTable([]);
            }
        })
        .catch(error => {
            console.error('Error loading top articles table:', error);
            displayTopArticlesTable([]);
        });
}

function displayTopArticlesTable(articles) {
    const tbody = document.getElementById('topArticlesTable');
    if (!tbody) return;

    if (articles.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #6b7280;">لا توجد مقالات</td></tr>';
        return;
    }

    const html = articles.map(article => `
        <tr>
            <td>
                <div style="max-width: 200px;">
                    <strong>${article.title}</strong>
                </div>
            </td>
            <td>${article.category_name || 'غير محدد'}</td>
            <td>${formatNumber(article.views)}</td>
            <td>${formatDate(article.created_at)}</td>
        </tr>
    `).join('');

    tbody.innerHTML = html;
}

// Load Categories Stats Table
function loadCategoriesStatsTable() {
    return fetch('php/dashboard-data.php?action=categories_stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayCategoriesStatsTable(data.categories);
            } else {
                console.error('Failed to load categories stats:', data.message);
                displayCategoriesStatsTable([]);
            }
        })
        .catch(error => {
            console.error('Error loading categories stats:', error);
            displayCategoriesStatsTable([]);
        });
}

function displayCategoriesStatsTable(categories) {
    const tbody = document.getElementById('categoriesStatsTable');
    if (!tbody) return;

    if (categories.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" style="text-align: center; color: #6b7280;">لا توجد تصنيفات</td></tr>';
        return;
    }

    const html = categories.map(category => `
        <tr>
            <td><strong>${category.name}</strong></td>
            <td>${formatNumber(category.articles_count)}</td>
            <td>${formatNumber(category.total_views)}</td>
            <td>${formatNumber(category.avg_views)}</td>
        </tr>
    `).join('');

    tbody.innerHTML = html;
}

// Analytics Action Functions
function refreshAnalytics() {
    showLoading();
    loadAnalytics();
    setTimeout(() => {
        hideLoading();
        showToast('تم تحديث التحليلات بنجاح', 'success');
    }, 1000);
}

function exportAnalytics() {
    const params = new URLSearchParams({
        action: 'export_analytics',
        format: 'pdf'
    });

    // Create download link
    const url = `php/dashboard-actions.php?${params}`;
    const link = document.createElement('a');
    link.href = url;
    link.download = `analytics_report_${new Date().toISOString().split('T')[0]}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showToast('تم بدء تصدير التقرير', 'success');
}

// Update Chart Functions
function updateTopArticlesChart() {
    const period = document.getElementById('topArticlesPeriod')?.value || 'month';

    fetch(`php/dashboard-data.php?action=top_articles&period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Destroy existing chart and create new one
                const canvas = document.getElementById('topArticlesChart');
                if (canvas && Chart.getChart(canvas)) {
                    Chart.getChart(canvas).destroy();
                }
                createTopArticlesChart(data.chart_data);
            }
        })
        .catch(error => console.error('Error updating top articles chart:', error));
}

function updateContentGrowthChart() {
    const period = document.getElementById('contentGrowthPeriod')?.value || '6months';

    fetch(`php/dashboard-data.php?action=content_growth&period=${period}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Destroy existing chart and create new one
                const canvas = document.getElementById('contentGrowthChart');
                if (canvas && Chart.getChart(canvas)) {
                    Chart.getChart(canvas).destroy();
                }
                createContentGrowthChart(data.chart_data);
            }
        })
        .catch(error => console.error('Error updating content growth chart:', error));
}
