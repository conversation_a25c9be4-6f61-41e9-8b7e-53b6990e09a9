-- ===================================================
-- إصلاح قاعدة البيانات - حذف الجداول القديمة وإنشاء الجديدة
-- ===================================================

USE `den_blog`;

-- حذف الجداول القديمة إذا كانت موجودة
DROP TABLE IF EXISTS `users-d`;
DROP TABLE IF EXISTS `articles`;
DROP TABLE IF EXISTS `categories`;
DROP TABLE IF EXISTS `doctors`;
DROP TABLE IF EXISTS `services`;

-- إنشاء جدول المستخدمين الجديد
CREATE TABLE `users_d` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `email` varchar(100) NOT NULL,
    `password` varchar(255) NOT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول التصنيفات
CREATE TABLE `categories` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `description` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول المقالات
CREATE TABLE `articles` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `content` text NOT NULL,
    `image` varchar(255) DEFAULT NULL,
    `category_id` int(11) NOT NULL,
    `author_id` int(11) NOT NULL,
    `status` enum('draft','published') NOT NULL DEFAULT 'draft',
    `views` int(11) DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`category_id`) REFERENCES `categories`(`id`),
    FOREIGN KEY (`author_id`) REFERENCES `users_d`(`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول الأطباء
CREATE TABLE `doctors` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) NOT NULL,
    `specialty` varchar(100) NOT NULL,
    `experience` int(11) DEFAULT 0,
    `phone` varchar(20),
    `bio` text,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إنشاء جدول الخدمات
CREATE TABLE `services` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `category` varchar(100) NOT NULL,
    `price` decimal(10,2) DEFAULT 0.00,
    `duration` varchar(50) DEFAULT NULL,
    `description` text DEFAULT NULL,
    `icon` varchar(50) DEFAULT 'fas fa-tooth',
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- إدراج المستخدمين
INSERT INTO `users_d` (`name`, `email`, `password`) VALUES
('admin', '<EMAIL>', 'admin123'),
('doctor1', '<EMAIL>', 'doctor123');

-- إدراج التصنيفات
INSERT INTO `categories` (`name`, `description`) VALUES
('العناية بالأسنان', 'نصائح وإرشادات للعناية اليومية بالأسنان'),
('تقويم الأسنان', 'معلومات حول تقويم الأسنان'),
('جراحة الأسنان', 'معلومات حول العمليات الجراحية'),
('طب أسنان الأطفال', 'العناية بأسنان الأطفال'),
('زراعة الأسنان', 'معلومات حول زراعة الأسنان'),
('تجميل الأسنان', 'علاجات تجميل الأسنان');

-- إدراج الأطباء
INSERT INTO `doctors` (`name`, `specialty`, `experience`, `phone`, `bio`) VALUES
('د. أحمد محمد علي', 'استشاري زراعة الأسنان', 15, '0501234567', 'استشاري زراعة الأسنان مع خبرة 15 عاماً'),
('د. فاطمة أحمد', 'أخصائية تقويم الأسنان', 12, '0501234568', 'أخصائية تقويم الأسنان مع خبرة 12 عاماً'),
('د. محمد حسن', 'جراح الفم والأسنان', 18, '0501234569', 'جراح متخصص في عمليات الفم والأسنان'),
('د. سارة عبدالله', 'أخصائية طب أسنان الأطفال', 10, '0501234570', 'أخصائية في طب أسنان الأطفال'),
('د. خالد أحمد', 'استشاري تجميل الأسنان', 8, '0501234571', 'استشاري تجميل وتبييض الأسنان'),
('د. نورا محمد', 'أخصائية علاج الجذور', 14, '0501234572', 'أخصائية في علاج جذور الأسنان');

-- إدراج المقالات
INSERT INTO `articles` (`title`, `content`, `category_id`, `author_id`, `status`, `views`) VALUES
('أهمية تنظيف الأسنان اليومي', 'تنظيف الأسنان يومياً أمر ضروري للحفاظ على صحة الفم والأسنان. يجب استخدام فرشاة أسنان ناعمة ومعجون أسنان يحتوي على الفلورايد...', 1, 1, 'published', 1250),
('تقويم الأسنان للكبار', 'تقويم الأسنان ليس مقتصراً على الأطفال فقط، بل يمكن للكبار أيضاً الاستفادة من علاج التقويم لتحسين شكل ووظيفة الأسنان...', 2, 2, 'published', 890),
('زراعة الأسنان: الحل الأمثل', 'زراعة الأسنان تعتبر الحل الأمثل لتعويض الأسنان المفقودة. تتميز بالثبات والمتانة وتعطي نتائج طبيعية...', 5, 1, 'published', 2100),
('العناية بأسنان الأطفال', 'العناية بأسنان الأطفال تبدأ منذ ظهور السن الأولى. من المهم تعليم الطفل عادات صحية للعناية بالأسنان...', 4, 2, 'published', 1580),
('تبييض الأسنان الطبيعي', 'هناك طرق طبيعية لتبييض الأسنان مثل استخدام بيكربونات الصوديوم والفراولة وزيت جوز الهند...', 6, 1, 'published', 950),
('علاج التهاب اللثة', 'التهاب اللثة مشكلة شائعة يمكن علاجها بالطرق الصحيحة. من المهم تنظيف الأسنان بانتظام واستخدام غسول الفم...', 1, 2, 'published', 1200),
('جراحة ضرس العقل', 'قد يحتاج بعض الأشخاص لإزالة ضرس العقل جراحياً إذا كان يسبب مشاكل أو لا يوجد مساحة كافية له...', 3, 1, 'draft', 0),
('أسباب تسوس الأسنان', 'تسوس الأسنان ينتج عن تراكم البكتيريا والأحماض على سطح الأسنان. يمكن الوقاية منه بالتنظيف المنتظم...', 1, 2, 'published', 1800);

-- إدراج الخدمات
INSERT INTO `services` (`name`, `category`, `price`, `duration`, `description`, `icon`) VALUES
('تنظيف الأسنان الشامل', 'الوقاية والعناية', 200.00, '45-60 دقيقة', 'تنظيف شامل للأسنان وإزالة الجير والبلاك', 'fas fa-tooth'),
('حشوات الأسنان التجميلية', 'العلاج والترميم', 300.00, '30-45 دقيقة', 'حشوات تجميلية بلون الأسنان الطبيعي', 'fas fa-fill-drip'),
('تبييض الأسنان المتقدم', 'التجميل', 800.00, '60-90 دقيقة', 'تبييض احترافي للأسنان بأحدث التقنيات', 'fas fa-smile'),
('زراعة الأسنان', 'الجراحة', 3500.00, '3-6 أشهر', 'زراعة أسنان بديلة بتقنية متقدمة', 'fas fa-user-md'),
('تقويم الأسنان', 'التقويم', 5000.00, '12-24 شهر', 'تقويم الأسنان للحصول على ابتسامة مثالية', 'fas fa-shield-alt'),
('طب أسنان الأطفال', 'الوقاية والعناية', 150.00, '30-45 دقيقة', 'فحص وعلاج أسنان الأطفال', 'fas fa-baby');

-- رسالة نجاح
SELECT 'تم إصلاح قاعدة البيانات بنجاح!' as message;
