<?php
// عرض تفاصيل المقال
require_once 'config/database.php';

// التحقق من وجود معرف المقال
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: articles.php');
    exit;
}

$articleId = (int)$_GET['id'];

// جلب تفاصيل المقال
$query = "SELECT a.*, c.name as category_name, u.name as author_name 
          FROM articles a 
          LEFT JOIN categories c ON a.category_id = c.id 
          LEFT JOIN users_d u ON a.author_id = u.id 
          WHERE a.id = ? AND a.status = 'published'";

$stmt = $conn->prepare($query);
$stmt->bind_param('i', $articleId);
$stmt->execute();
$result = $stmt->get_result();

if (!$article = $result->fetch_assoc()) {
    header('Location: articles.php');
    exit;
}

// تحديث عداد المشاهدات
$updateQuery = "UPDATE articles SET views = views + 1 WHERE id = ?";
$updateStmt = $conn->prepare($updateQuery);
$updateStmt->bind_param('i', $articleId);
$updateStmt->execute();
$article['views']++;

$relatedQuery = "SELECT a.*, c.name as category_name, u.name as author_name 
                 FROM articles a 
                 LEFT JOIN categories c ON a.category_id = c.id 
                 LEFT JOIN users_d u ON a.author_id = u.id 
                 WHERE a.category_id = ? AND a.id != ? AND a.status = 'published' 
                 ORDER BY a.created_at DESC 
                 LIMIT 3";

$relatedStmt = $conn->prepare($relatedQuery);
$relatedStmt->bind_param('ii', $article['category_id'], $articleId);
$relatedStmt->execute();
$relatedResult = $relatedStmt->get_result();

$relatedArticles = [];
while ($row = $relatedResult->fetch_assoc()) {
    $relatedArticles[] = $row;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($article['title']); ?> - سمايل ديزاين لطب الأسنان</title>
    <meta name="description" content="<?php echo htmlspecialchars(substr($article['content'], 0, 160)); ?>">
    <link rel="stylesheet" href="style/styles.css">
    <link rel="stylesheet" href="style/articles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .article-detail {
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .article-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 2rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .article-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 1rem;
            line-height: 1.3;
        }
        
        .article-meta-detail {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #6c757d;
            font-size: 0.95rem;
        }
        
        .meta-item i {
            color: #d4af37;
        }
        
        .article-category-badge {
            display: inline-block;
            background: linear-gradient(135deg, #d4af37, #f4d03f);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .article-content {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #2c3e50;
            margin: 2rem 0;
        }
        
        .article-content p {
            margin-bottom: 1.5rem;
        }
        
        .article-actions {
            display: flex;
            justify-content: center;
            gap: 1rem;
            margin: 2rem 0;
            padding: 2rem 0;
            border-top: 2px solid #f0f0f0;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            border: 2px solid #d4af37;
            background: white;
            color: #d4af37;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #d4af37;
            color: white;
        }
        
        .related-articles {
            margin-top: 3rem;
        }

        .related-title {
            text-align: center;
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 2rem;
        }

        .related-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }

        .related-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border: 1px solid #f0f0f0;
        }

        .related-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(212, 175, 55, 0.2);
            border-color: #d4af37;
        }

        .related-image {
            width: 100%;
            height: 150px;
            overflow: hidden;
        }

        .related-content {
            padding: 1.5rem;
        }

        .related-card h4 {
            font-size: 1.2rem;
            margin-bottom: 0.8rem;
            line-height: 1.4;
        }

        .related-card h4 a {
            color: #2c3e50;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .related-card h4 a:hover {
            color: #d4af37;
        }

        .related-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1rem;
            font-size: 0.95rem;
        }

        .read-more-btn {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #f0f0f0;
        }

        .read-more-btn span {
            color: #d4af37;
            font-weight: 600;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
        }

        .related-card:hover .read-more-btn span {
            color: #b8860b;
            transform: translateX(-5px);
        }
        
        .back-to-articles {
            position: fixed;
            top: 100px;
            right: 2rem;
            background: #d4af37;
            color: white;
            padding: 1rem;
            border-radius: 50%;
            text-decoration: none;
            box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-to-articles:hover {
            background: #b8860b;
            transform: scale(1.1);
            color: white;
        }
        
        @media (max-width: 768px) {
            .article-detail {
                margin: 1rem;
                padding: 1.5rem;
            }
            
            .article-title {
                font-size: 2rem;
            }
            
            .article-meta-detail {
                flex-direction: column;
                gap: 1rem;
            }
            
            .article-actions {
                flex-direction: column;
            }
            
            .related-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .related-card {
                margin-bottom: 1rem;
            }

            .related-content {
                padding: 1.2rem;
            }

            .related-card h4 {
                font-size: 1.1rem;
            }

            .back-to-articles {
                right: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Back to Articles Button -->
    <a href="articles.php" class="back-to-articles" title="العودة للمقالات">
        <i class="fas fa-arrow-right"></i>
    </a>

    <!-- Header -->
    <header class="header">
        <nav class="navbar">
            <div class="nav-container">
                <div class="logo">
                    <i class="fas fa-tooth"></i>
                    <span>سمايل ديزاين</span>
                </div>
                <ul class="nav-menu">
                    <li><a href="index.php" class="nav-link">الرئيسية</a></li>
                    <li><a href="index.php#services" class="nav-link">الخدمات</a></li>
                    <li><a href="about.php" class="nav-link">عن المركز</a></li>
                    <li><a href="team.php" class="nav-link">الفريق الطبي</a></li>
                    <li><a href="articles.php" class="nav-link active">المقالات</a></li>
                    <li><a href="index.php#contact" class="nav-link">تواصل معنا</a></li>
                </ul>
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Page Header -->
    <section class="page-header">
        <div class="container">
            <h1>تفاصيل المقال</h1>
            <nav class="breadcrumb">
                <a href="index.php">الرئيسية</a>
                <span>/</span>
                <a href="articles.php">المقالات</a>
                <span>/</span>
                <span><?php echo htmlspecialchars($article['title']); ?></span>
            </nav>
        </div>
    </section>

    <!-- Article Detail Section -->
    <section class="articles-section">
        <div class="container">
            <article class="article-detail">
                <header class="article-header">
                    <h1 class="article-title"><?php echo htmlspecialchars($article['title']); ?></h1>

                    <div class="article-meta-detail">
                        <div class="meta-item">
                            <i class="fas fa-calendar"></i>
                            <span><?php echo date('d M Y', strtotime($article['created_at'])); ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-user"></i>
                            <span><?php echo htmlspecialchars($article['author_name']); ?></span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-eye"></i>
                            <span><?php echo number_format($article['views']); ?> مشاهدة</span>
                        </div>
                    </div>

                    <div class="article-category-badge">
                        <?php echo htmlspecialchars($article['category_name']); ?>
                    </div>
                </header>

                <!-- Article Image -->
                <?php
                $imagePath = '';
                if (!empty($article['image'])) {
                    // التحقق من وجود الصورة في مجلد uploads
                    if (file_exists('uploads/articles/' . $article['image'])) {
                        $imagePath = 'uploads/articles/' . $article['image'];
                    } elseif (file_exists('imgs/' . $article['image'])) {
                        $imagePath = 'imgs/' . $article['image'];
                    }
                }

                // إذا لم توجد صورة، استخدم الصورة الافتراضية
                if (empty($imagePath)) {
                    $imagePath = 'imgs/default-article.svg';
                }
                ?>

                <?php if (file_exists($imagePath)): ?>
                    <div style="text-align: center; margin: 2rem 0;">
                        <img src="<?php echo htmlspecialchars($imagePath); ?>"
                             alt="<?php echo htmlspecialchars($article['title']); ?>"
                             style="width: 100%; max-width: 600px; height: auto; border-radius: 10px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                    </div>
                <?php else: ?>
                    <div style="text-align: center; margin: 2rem 0; padding: 4rem; background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%); border-radius: 10px; max-width: 600px; margin: 2rem auto;">
                        <i class="fas fa-file-alt" style="font-size: 5rem; color: white; opacity: 0.7;"></i>
                        <p style="color: white; margin-top: 1rem; font-size: 1.3rem;">صورة المقال</p>
                    </div>
                <?php endif; ?>

                <div class="article-content">
                    <?php echo nl2br(htmlspecialchars($article['content'])); ?>
                </div>

                <div class="article-actions">
                    <a href="articles.php" class="action-btn">
                        <i class="fas fa-list"></i>
                        جميع المقالات
                    </a>
                    <a href="articles.php?category=<?php echo urlencode($article['category_name']); ?>" class="action-btn">
                        <i class="fas fa-tag"></i>
                        مقالات <?php echo htmlspecialchars($article['category_name']); ?>
                    </a>
                    <a href="javascript:window.print()" class="action-btn">
                        <i class="fas fa-print"></i>
                        طباعة
                    </a>
                </div>
            </article>

            <!-- Related Articles -->
            <?php if (!empty($relatedArticles)): ?>
            <section class="related-articles">
                <h2 class="related-title">مقالات ذات صلة</h2>
                <div class="related-grid">
                    <?php foreach ($relatedArticles as $related): ?>
                    <div class="related-card" onclick="window.location.href='article.php?id=<?php echo $related['id']; ?>'" style="cursor: pointer;">
                        <div class="related-image">
                            <?php
                            $relatedImagePath = '';
                            if (!empty($related['image'])) {
                                if (file_exists('uploads/articles/' . $related['image'])) {
                                    $relatedImagePath = 'uploads/articles/' . $related['image'];
                                } elseif (file_exists('imgs/' . $related['image'])) {
                                    $relatedImagePath = 'imgs/' . $related['image'];
                                }
                            }

                            if (empty($relatedImagePath)) {
                                $relatedImagePath = 'imgs/default-article.svg';
                            }
                            ?>

                            <?php if (file_exists($relatedImagePath)): ?>
                                <img src="<?php echo htmlspecialchars($relatedImagePath); ?>"
                                     alt="<?php echo htmlspecialchars($related['title']); ?>"
                                     style="width: 100%; height: 150px; object-fit: cover; border-radius: 8px;">
                            <?php else: ?>
                                <div style="width: 100%; height: 150px; background: linear-gradient(135deg, #d4af37 0%, #f4d03f 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                    <i class="fas fa-file-alt" style="font-size: 2rem; color: white; opacity: 0.7;"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="related-content">
                            <h4><a href="article.php?id=<?php echo $related['id']; ?>" onclick="event.stopPropagation();"><?php echo htmlspecialchars($related['title']); ?></a></h4>
                            <p><?php echo htmlspecialchars(substr($related['content'], 0, 100)) . '...'; ?></p>
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span><?php echo date('d M Y', strtotime($related['created_at'])); ?></span>
                            </div>
                            <div class="read-more-btn">
                                <span>اقرأ المزيد <i class="fas fa-arrow-left"></i></span>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>
            <?php endif; ?>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <div class="logo">
                        <i class="fas fa-tooth"></i>
                        <span>سمايل ديزاين لطب الأسنان</span>
                    </div>
                    <p>نقدم أفضل خدمات طب الأسنان بأحدث التقنيات وأعلى معايير الجودة مع تصميم ابتسامة مثالية</p>
                </div>
                <div class="footer-section">
                    <h4>روابط سريعة</h4>
                    <ul>
                        <li><a href="index.php">الرئيسية</a></li>
                        <li><a href="index.php#services">الخدمات</a></li>
                        <li><a href="index.php#about">عن العيادة</a></li>
                        <li><a href="index.php#team">الفريق الطبي</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 سمايل ديزاين لطب الأسنان. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script src="js/script.js"></script>
</body>
</html>
