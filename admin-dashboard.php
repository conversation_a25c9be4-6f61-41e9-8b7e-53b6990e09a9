<?php
session_start();
require_once 'config/database.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    header('Location: signin.php');
    exit();
}

// جلب بيانات المستخدم
$user_id = $_SESSION['user_id'];
$user_query = "SELECT * FROM `users_d` WHERE id = ?";
$stmt = $conn->prepare($user_query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$user_result = $stmt->get_result();
$user = $user_result->fetch_assoc();

// جلب الإحصائيات
$stats = [];

// عدد المقالات
$articles_query = "SELECT COUNT(*) as total FROM articles";
$articles_result = $conn->query($articles_query);
$stats['articles'] = $articles_result->fetch_assoc()['total'];

// عدد التصنيفات
$categories_query = "SELECT COUNT(*) as total FROM categories";
$categories_result = $conn->query($categories_query);
$stats['categories'] = $categories_result->fetch_assoc()['total'];

// عدد الأطباء
$doctors_query = "SELECT COUNT(*) as total FROM doctors";
$doctors_result = $conn->query($doctors_query);
$stats['doctors'] = $doctors_result->fetch_assoc()['total'];

// عدد الخدمات
$services_query = "SELECT COUNT(*) as total FROM services";
$services_result = $conn->query($services_query);
$stats['services'] = $services_result->fetch_assoc()['total'];

// إجمالي المشاهدات
$views_query = "SELECT SUM(views) as total FROM articles";
$views_result = $conn->query($views_query);
$stats['views'] = $views_result->fetch_assoc()['total'] ?? 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - إدارة المدونة الطبية</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="style/admin-dashboard.css">
    <link rel="stylesheet" href="style/chrome-compatibility.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="admin-body">
    <!-- Header -->
    <header class="admin-header">
        <nav class="admin-nav">
            <div class="logo">
                <i class="fas fa-tooth"></i>
                <span>لوحة التحكم الطبية</span>
            </div>
            <div class="admin-actions">
                <div class="global-search">
                    <div class="search-container">
                        <input type="text" id="globalSearchInput" placeholder="البحث في جميع البيانات..." class="search-input">
                        <button type="button" onclick="performGlobalSearch()" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div id="searchResults" class="search-results"></div>
                </div>
                <div class="admin-user">
                    <i class="fas fa-user-circle"></i>
                    <span>مرحباً، <?php echo htmlspecialchars($user['name']); ?></span>
                </div>
                <a href="logout.php" class="btn btn-secondary">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </a>
                <button class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </nav>
    </header>

    <!-- Sidebar -->
    <aside class="admin-sidebar">
        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item active">
                    <a href="#dashboard" class="nav-link" data-section="dashboard">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>لوحة المعلومات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#articles" class="nav-link" data-section="articles">
                        <i class="fas fa-newspaper"></i>
                        <span>إدارة المقالات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#categories" class="nav-link" data-section="categories">
                        <i class="fas fa-tags"></i>
                        <span>إدارة التصنيفات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#doctors" class="nav-link" data-section="doctors">
                        <i class="fas fa-user-md"></i>
                        <span>إدارة الأطباء</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#services" class="nav-link" data-section="services">
                        <i class="fas fa-cogs"></i>
                        <span>إدارة الخدمات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#analytics" class="nav-link" data-section="analytics">
                        <i class="fas fa-chart-bar"></i>
                        <span>التحليلات</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#settings" class="nav-link" data-section="settings">
                        <i class="fas fa-cog"></i>
                        <span>الإعدادات</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <main class="admin-main">
        <!-- Dashboard Section -->
        <section id="dashboard" class="admin-section active">
            <div class="section-header">
                <div>
                    <h1>لوحة المعلومات</h1>
                    <p>نظرة عامة على إحصائيات الموقع والأنشطة الحديثة</p>
                </div>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="refreshDashboard()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['articles']; ?></h3>
                        <p>إجمالي المقالات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['categories']; ?></h3>
                        <p>التصنيفات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['doctors']; ?></h3>
                        <p>الأطباء</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo $stats['services']; ?></h3>
                        <p>الخدمات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="stat-content">
                        <h3><?php echo number_format($stats['views']); ?></h3>
                        <p>إجمالي المشاهدات</p>
                    </div>
                </div>
            </div>

            <!-- Dashboard Widgets -->
            <div class="dashboard-widgets">
                <div class="widget">
                    <div class="widget-header">
                        <h3>المقالات الحديثة</h3>
                        <a href="#articles" class="view-all" onclick="showSection('articles')">عرض الكل</a>
                    </div>
                    <div class="widget-content" id="recent-articles">
                        <!-- سيتم تحميل المحتوى بواسطة JavaScript -->
                    </div>
                </div>
                <div class="widget">
                    <div class="widget-header">
                        <h3>الأطباء المضافون حديثاً</h3>
                        <a href="#doctors" class="view-all" onclick="showSection('doctors')">عرض الكل</a>
                    </div>
                    <div class="widget-content" id="recent-doctors">
                        <!-- سيتم تحميل المحتوى بواسطة JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Quick Analytics -->
            <div class="quick-analytics">
                <div class="quick-chart-card">
                    <h3>نظرة سريعة - التصنيفات</h3>
                    <canvas id="dashboardCategoriesChart" width="300" height="200"></canvas>
                    <div class="chart-footer">
                        <a href="#analytics" onclick="showSection('analytics')" class="view-detailed">
                            <i class="fas fa-chart-pie"></i>
                            عرض التحليل المفصل
                        </a>
                    </div>
                </div>
                <div class="quick-chart-card">
                    <h3>اتجاه المشاهدات</h3>
                    <canvas id="dashboardViewsChart" width="300" height="200"></canvas>
                    <div class="chart-footer">
                        <a href="#analytics" onclick="showSection('analytics')" class="view-detailed">
                            <i class="fas fa-chart-line"></i>
                            عرض التحليل المفصل
                        </a>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <div class="action-card" onclick="showSection('articles')">
                    <div class="action-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="action-content">
                        <h4>إضافة مقال جديد</h4>
                        <p>ابدأ في كتابة مقال جديد</p>
                    </div>
                </div>
                <div class="action-card" onclick="showSection('doctors')">
                    <div class="action-icon">
                        <i class="fas fa-user-md"></i>
                    </div>
                    <div class="action-content">
                        <h4>إضافة طبيب</h4>
                        <p>أضف طبيب جديد للفريق</p>
                    </div>
                </div>
                <div class="action-card" onclick="showSection('analytics')">
                    <div class="action-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="action-content">
                        <h4>عرض التحليلات</h4>
                        <p>تحليلات مفصلة للأداء</p>
                    </div>
                </div>
                <div class="action-card" onclick="showSection('settings')">
                    <div class="action-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="action-content">
                        <h4>الإعدادات</h4>
                        <p>إعدادات النظام والموقع</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Articles Section -->
        <section id="articles" class="admin-section">
            <div class="section-header">
                <div>
                    <h1>إدارة المقالات</h1>
                    <p>إضافة وتعديل وحذف المقالات مع إدارة متقدمة</p>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" onclick="exportArticles()">
                        <i class="fas fa-download"></i>
                        تصدير
                    </button>
                    <button class="btn btn-secondary" onclick="bulkActions()">
                        <i class="fas fa-tasks"></i>
                        عمليات متعددة
                    </button>
                    <button class="btn btn-primary" onclick="openModal('articleModal')">
                        <i class="fas fa-plus"></i>
                        إضافة مقال جديد
                    </button>
                </div>
            </div>

            <!-- Advanced Filters -->
            <div class="admin-filters">
                <div class="filter-row">
                    <div class="filter-group">
                        <label>البحث:</label>
                        <input type="text" id="articleSearch" placeholder="البحث في العنوان والمحتوى..." class="filter-input">
                    </div>
                    <div class="filter-group">
                        <label>التصنيف:</label>
                        <select id="categoryFilter" class="filter-select">
                            <option value="">جميع التصنيفات</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>الحالة:</label>
                        <select id="statusFilter" class="filter-select">
                            <option value="">جميع الحالات</option>
                            <option value="published">منشور</option>
                            <option value="draft">مسودة</option>
                        </select>
                    </div>
                </div>
                <div class="filter-row">
                    <div class="filter-group">
                        <label>الكاتب:</label>
                        <select id="authorFilter" class="filter-select">
                            <option value="">جميع الكتاب</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label>تاريخ الإنشاء:</label>
                        <input type="date" id="dateFromFilter" class="filter-input">
                        <span>إلى</span>
                        <input type="date" id="dateToFilter" class="filter-input">
                    </div>
                    <div class="filter-group">
                        <label>المشاهدات:</label>
                        <select id="viewsFilter" class="filter-select">
                            <option value="">جميع المقالات</option>
                            <option value="high">أكثر من 1000 مشاهدة</option>
                            <option value="medium">500-1000 مشاهدة</option>
                            <option value="low">أقل من 500 مشاهدة</option>
                            <option value="zero">بدون مشاهدات</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="btn btn-secondary" onclick="filterArticles()">
                            <i class="fas fa-search"></i>
                            بحث
                        </button>
                        <button class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i>
                            مسح
                        </button>
                    </div>
                </div>
            </div>

            <!-- Articles Stats -->
            <div class="articles-stats">
                <div class="stat-item">
                    <span class="stat-number" id="totalArticles">0</span>
                    <span class="stat-label">إجمالي المقالات</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="publishedArticles">0</span>
                    <span class="stat-label">منشور</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="draftArticles">0</span>
                    <span class="stat-label">مسودة</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number" id="totalViews">0</span>
                    <span class="stat-label">إجمالي المشاهدات</span>
                </div>
            </div>

            <!-- Bulk Actions -->
            <div class="bulk-actions" id="bulkActionsPanel" style="display: none;">
                <div class="bulk-actions-header">
                    <span id="selectedCount">0</span> مقال محدد
                    <div class="bulk-actions-buttons">
                        <button class="btn btn-secondary" onclick="bulkChangeStatus('published')">
                            <i class="fas fa-eye"></i>
                            نشر
                        </button>
                        <button class="btn btn-secondary" onclick="bulkChangeStatus('draft')">
                            <i class="fas fa-eye-slash"></i>
                            إخفاء
                        </button>
                        <button class="btn btn-secondary" onclick="bulkChangeCategory()">
                            <i class="fas fa-tag"></i>
                            تغيير التصنيف
                        </button>
                        <button class="btn btn-danger" onclick="bulkDelete()">
                            <i class="fas fa-trash"></i>
                            حذف
                        </button>
                        <button class="btn btn-secondary" onclick="cancelBulkActions()">
                            <i class="fas fa-times"></i>
                            إلغاء
                        </button>
                    </div>
                </div>
            </div>

            <!-- Articles Table -->
            <div class="admin-table-container">
                <div class="table-controls">
                    <div class="table-controls-left">
                        <label>
                            <input type="checkbox" id="selectAllArticles" onchange="toggleSelectAll()">
                            تحديد الكل
                        </label>
                        <span class="table-info" id="tableInfo">عرض 0 من 0 مقال</span>
                    </div>
                    <div class="table-controls-right">
                        <label>عرض:</label>
                        <select id="articlesPerPage" onchange="changeArticlesPerPage()">
                            <option value="10">10</option>
                            <option value="25" selected>25</option>
                            <option value="50">50</option>
                            <option value="100">100</option>
                        </select>
                        <span>مقال في الصفحة</span>
                    </div>
                </div>

                <table class="admin-table">
                    <thead>
                        <tr>
                            <th width="40">
                                <input type="checkbox" id="selectAllHeader" onchange="toggleSelectAll()">
                            </th>
                            <th onclick="sortArticles('title')" class="sortable">
                                العنوان <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortArticles('category_name')" class="sortable">
                                التصنيف <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortArticles('author_name')" class="sortable">
                                الكاتب <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortArticles('status')" class="sortable">
                                الحالة <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortArticles('views')" class="sortable">
                                المشاهدات <i class="fas fa-sort"></i>
                            </th>
                            <th onclick="sortArticles('created_at')" class="sortable">
                                تاريخ الإنشاء <i class="fas fa-sort"></i>
                            </th>
                            <th width="150">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="articlesTableBody">
                        <!-- سيتم تحميل البيانات بواسطة JavaScript -->
                    </tbody>
                </table>

                <!-- Pagination -->
                <div class="pagination-container" id="articlesPagination">
                    <!-- سيتم إنشاء أزرار التصفح بواسطة JavaScript -->
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section id="categories" class="admin-section">
            <div class="section-header">
                <div>
                    <h1>إدارة التصنيفات</h1>
                    <p>إضافة وتعديل وحذف تصنيفات المقالات</p>
                </div>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="openModal('categoryModal')">
                        <i class="fas fa-plus"></i>
                        إضافة تصنيف جديد
                    </button>
                </div>
            </div>

            <!-- Categories Grid -->
            <div class="categories-grid" id="categoriesGrid">
                <!-- سيتم تحميل البيانات بواسطة JavaScript -->
            </div>
        </section>

        <!-- Doctors Section -->
        <section id="doctors" class="admin-section">
            <div class="section-header">
                <div>
                    <h1>إدارة الأطباء</h1>
                    <p>إضافة وتعديل وحذف بيانات الأطباء</p>
                </div>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="openModal('doctorModal')">
                        <i class="fas fa-plus"></i>
                        إضافة طبيب جديد
                    </button>
                </div>
            </div>

            <!-- Doctors Grid -->
            <div class="team-management-grid" id="doctorsGrid">
                <!-- سيتم تحميل البيانات بواسطة JavaScript -->
            </div>
        </section>

        <!-- Services Section -->
        <section id="services" class="admin-section">
            <div class="section-header">
                <div>
                    <h1>إدارة الخدمات</h1>
                    <p>إضافة وتعديل وحذف الخدمات الطبية</p>
                </div>
                <div class="section-actions">
                    <button class="btn btn-primary" onclick="openModal('serviceModal')">
                        <i class="fas fa-plus"></i>
                        إضافة خدمة جديدة
                    </button>
                </div>
            </div>

            <!-- Services Grid -->
            <div class="services-management-grid" id="servicesGrid">
                <!-- سيتم تحميل البيانات بواسطة JavaScript -->
            </div>
        </section>

        <!-- Analytics Section -->
        <section id="analytics" class="admin-section">
            <div class="section-header">
                <div>
                    <h1>التحليلات والإحصائيات</h1>
                    <p>تقارير مفصلة عن أداء الموقع والمحتوى</p>
                </div>
                <div class="section-actions">
                    <button class="btn btn-secondary" onclick="refreshAnalytics()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث البيانات
                    </button>
                    <button class="btn btn-secondary" onclick="exportAnalytics()">
                        <i class="fas fa-download"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>

            <!-- Analytics Summary Cards -->
            <div class="analytics-summary">
                <div class="summary-card">
                    <div class="summary-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="totalPageViews">0</h3>
                        <p>إجمالي المشاهدات</p>
                        <small class="trend positive" id="viewsTrend">+12% من الشهر الماضي</small>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">
                        <i class="fas fa-newspaper"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="totalArticles">0</h3>
                        <p>إجمالي المقالات</p>
                        <small class="trend positive" id="articlesTrend">+5 مقالات جديدة</small>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="avgViews">0</h3>
                        <p>متوسط المشاهدات</p>
                        <small class="trend neutral" id="avgTrend">لكل مقال</small>
                    </div>
                </div>
                <div class="summary-card">
                    <div class="summary-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="summary-content">
                        <h3 id="topCategory">-</h3>
                        <p>أكثر التصنيفات نشاطاً</p>
                        <small class="trend positive" id="topCategoryCount">0 مقال</small>
                    </div>
                </div>
            </div>

            <!-- Analytics Charts Grid -->
            <div class="analytics-grid">
                <div class="analytics-card large">
                    <div class="card-header">
                        <h3>أكثر المقالات مشاهدة</h3>
                        <div class="card-actions">
                            <select id="topArticlesPeriod" onchange="updateTopArticlesChart()">
                                <option value="week">هذا الأسبوع</option>
                                <option value="month" selected>هذا الشهر</option>
                                <option value="year">هذا العام</option>
                                <option value="all">جميع الأوقات</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="topArticlesChart"></canvas>
                    </div>
                </div>

                <div class="analytics-card large">
                    <div class="card-header">
                        <h3>نمو المحتوى الشهري</h3>
                        <div class="card-actions">
                            <select id="contentGrowthPeriod" onchange="updateContentGrowthChart()">
                                <option value="6months" selected>آخر 6 أشهر</option>
                                <option value="year">آخر سنة</option>
                                <option value="2years">آخر سنتين</option>
                            </select>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="contentGrowthChart"></canvas>
                    </div>
                </div>

                <div class="analytics-card medium">
                    <div class="card-header">
                        <h3>توزيع المقالات حسب التصنيف</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="categoriesDistributionChart"></canvas>
                    </div>
                </div>

                <div class="analytics-card medium">
                    <div class="card-header">
                        <h3>إحصائيات المشاهدات اليومية</h3>
                    </div>
                    <div class="chart-container">
                        <canvas id="dailyViewsChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Analytics Tables -->
            <div class="analytics-tables">
                <div class="analytics-table-card">
                    <h3>أفضل 10 مقالات</h3>
                    <div class="table-container">
                        <table class="analytics-table">
                            <thead>
                                <tr>
                                    <th>المقال</th>
                                    <th>التصنيف</th>
                                    <th>المشاهدات</th>
                                    <th>تاريخ النشر</th>
                                </tr>
                            </thead>
                            <tbody id="topArticlesTable">
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="analytics-table-card">
                    <h3>إحصائيات التصنيفات</h3>
                    <div class="table-container">
                        <table class="analytics-table">
                            <thead>
                                <tr>
                                    <th>التصنيف</th>
                                    <th>عدد المقالات</th>
                                    <th>إجمالي المشاهدات</th>
                                    <th>متوسط المشاهدات</th>
                                </tr>
                            </thead>
                            <tbody id="categoriesStatsTable">
                                <!-- سيتم ملؤها بواسطة JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="admin-section">
            <div class="section-header">
                <div>
                    <h1>الإعدادات</h1>
                    <p>إعدادات النظام والموقع</p>
                </div>
            </div>

            <!-- Settings Container -->
            <div class="settings-container">
                <div class="settings-card">
                    <h3>إعدادات الموقع</h3>
                    <form class="settings-form">
                        <div class="form-group">
                            <label>اسم الموقع</label>
                            <input type="text" value="المدونة الطبية" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>وصف الموقع</label>
                            <textarea class="form-control" rows="3">موقع متخصص في المحتوى الطبي والصحي</textarea>
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني للتواصل</label>
                            <input type="email" value="<EMAIL>" class="form-control">
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </form>
                </div>
                <div class="settings-card">
                    <h3>إعدادات المستخدم</h3>
                    <form class="settings-form">
                        <div class="form-group">
                            <label>الاسم</label>
                            <input type="text" value="<?php echo htmlspecialchars($user['name']); ?>" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني</label>
                            <input type="email" value="<?php echo htmlspecialchars($user['email']); ?>" class="form-control">
                        </div>
                        <div class="form-group">
                            <label>كلمة المرور الجديدة</label>
                            <input type="password" placeholder="اتركها فارغة إذا لم ترد تغييرها" class="form-control">
                        </div>
                        <button type="submit" class="btn btn-primary">تحديث البيانات</button>
                    </form>
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <!-- Article Modal -->
    <div id="articleModal" class="modal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h2>إضافة/تعديل مقال</h2>
                <button class="modal-close" onclick="closeModal('articleModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="articleForm">
                    <div class="form-tabs">
                        <div class="tab-buttons">
                            <button type="button" class="tab-btn active" onclick="switchTab('basic')">المعلومات الأساسية</button>
                            <button type="button" class="tab-btn" onclick="switchTab('content')">المحتوى</button>
                            <button type="button" class="tab-btn" onclick="switchTab('seo')">تحسين محركات البحث</button>
                            <button type="button" class="tab-btn" onclick="switchTab('settings')">الإعدادات</button>
                        </div>

                        <!-- Basic Info Tab -->
                        <div id="basic-tab" class="tab-content active">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>عنوان المقال *</label>
                                    <input type="text" name="title" required placeholder="أدخل عنوان المقال">
                                    <small class="form-help">عنوان واضح وجذاب للمقال</small>
                                </div>
                                <div class="form-group">
                                    <label>الرابط المختصر</label>
                                    <input type="text" name="slug" placeholder="سيتم إنشاؤه تلقائياً">
                                    <small class="form-help">رابط صديق لمحركات البحث</small>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>التصنيف *</label>
                                    <select name="category_id" required>
                                        <option value="">اختر التصنيف</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>الكلمات المفتاحية</label>
                                    <input type="text" name="tags" placeholder="كلمة1، كلمة2، كلمة3">
                                    <small class="form-help">افصل بين الكلمات بفاصلة</small>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>ملخص المقال</label>
                                <textarea name="excerpt" rows="3" placeholder="ملخص قصير عن المقال"></textarea>
                                <small class="form-help">ملخص يظهر في نتائج البحث والقوائم</small>
                            </div>
                            <div class="form-group">
                                <label>صورة المقال</label>
                                <div class="image-upload">
                                    <input type="file" name="article_image" accept="image/*" onchange="previewImage(this, 'articleImagePreview')">
                                    <div class="image-preview" id="articleImagePreview">
                                        <i class="fas fa-cloud-upload-alt"></i>
                                        <p>اسحب الصورة هنا أو انقر للاختيار</p>
                                        <small>الحد الأقصى: 5MB - الصيغ المدعومة: JPG, PNG, GIF</small>
                                    </div>
                                </div>
                                <small class="form-help">صورة تمثيلية للمقال (اختيارية)</small>
                            </div>
                        </div>

                        <!-- Content Tab -->
                        <div id="content-tab" class="tab-content">
                            <div class="form-group">
                                <label>محتوى المقال *</label>
                                <div class="editor-toolbar">
                                    <button type="button" class="editor-btn" onclick="formatText('bold')">
                                        <i class="fas fa-bold"></i>
                                    </button>
                                    <button type="button" class="editor-btn" onclick="formatText('italic')">
                                        <i class="fas fa-italic"></i>
                                    </button>
                                    <button type="button" class="editor-btn" onclick="formatText('underline')">
                                        <i class="fas fa-underline"></i>
                                    </button>
                                    <button type="button" class="editor-btn" onclick="insertList('ul')">
                                        <i class="fas fa-list-ul"></i>
                                    </button>
                                    <button type="button" class="editor-btn" onclick="insertList('ol')">
                                        <i class="fas fa-list-ol"></i>
                                    </button>
                                    <button type="button" class="editor-btn" onclick="insertLink()">
                                        <i class="fas fa-link"></i>
                                    </button>
                                </div>
                                <textarea name="content" rows="15" required placeholder="اكتب محتوى المقال هنا..."></textarea>
                                <div class="content-stats">
                                    <span id="wordCount">0 كلمة</span>
                                    <span id="charCount">0 حرف</span>
                                </div>
                            </div>
                        </div>

                        <!-- SEO Tab -->
                        <div id="seo-tab" class="tab-content">
                            <div class="form-group">
                                <label>عنوان SEO</label>
                                <input type="text" name="seo_title" placeholder="عنوان محسن لمحركات البحث">
                                <small class="form-help">يُفضل أن يكون بين 50-60 حرف</small>
                            </div>
                            <div class="form-group">
                                <label>وصف SEO</label>
                                <textarea name="seo_description" rows="3" placeholder="وصف المقال لمحركات البحث"></textarea>
                                <small class="form-help">يُفضل أن يكون بين 150-160 حرف</small>
                            </div>
                            <div class="form-group">
                                <label>الكلمات المفتاحية لمحركات البحث</label>
                                <input type="text" name="seo_keywords" placeholder="كلمة1، كلمة2، كلمة3">
                            </div>
                        </div>

                        <!-- Settings Tab -->
                        <div id="settings-tab" class="tab-content">
                            <div class="form-row">
                                <div class="form-group">
                                    <label>حالة المقال</label>
                                    <select name="status">
                                        <option value="draft">مسودة</option>
                                        <option value="published">منشور</option>
                                        <option value="scheduled">مجدول</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>تاريخ النشر</label>
                                    <input type="datetime-local" name="publish_date">
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>السماح بالتعليقات</label>
                                    <label class="switch">
                                        <input type="checkbox" name="allow_comments" checked>
                                        <span class="slider"></span>
                                    </label>
                                </div>
                                <div class="form-group">
                                    <label>مقال مميز</label>
                                    <label class="switch">
                                        <input type="checkbox" name="featured">
                                        <span class="slider"></span>
                                    </label>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>ترتيب المقال</label>
                                <input type="number" name="sort_order" value="0" min="0">
                                <small class="form-help">ترتيب المقال في القائمة (0 = تلقائي)</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('articleModal')">إلغاء</button>
                        <button type="button" class="btn btn-secondary" onclick="saveAsDraft()">حفظ كمسودة</button>
                        <button type="button" class="btn btn-secondary" onclick="previewArticle()">معاينة</button>
                        <button type="submit" class="btn btn-primary">حفظ ونشر</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Category Modal -->
    <div id="categoryModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إضافة/تعديل تصنيف</h2>
                <button class="modal-close" onclick="closeModal('categoryModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="categoryForm">
                    <div class="form-group">
                        <label>اسم التصنيف</label>
                        <input type="text" name="name" required>
                    </div>
                    <div class="form-group">
                        <label>وصف التصنيف</label>
                        <textarea name="description" rows="4"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('categoryModal')">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التصنيف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Doctor Modal -->
    <div id="doctorModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إضافة/تعديل طبيب</h2>
                <button class="modal-close" onclick="closeModal('doctorModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="doctorForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم الطبيب</label>
                            <input type="text" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>التخصص</label>
                            <input type="text" name="specialty" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>سنوات الخبرة</label>
                            <input type="number" name="experience" min="0">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="tel" name="phone">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>نبذة عن الطبيب</label>
                        <textarea name="bio" rows="4"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('doctorModal')">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ بيانات الطبيب</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Service Modal -->
    <div id="serviceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>إضافة/تعديل خدمة</h2>
                <button class="modal-close" onclick="closeModal('serviceModal')">&times;</button>
            </div>
            <div class="modal-body">
                <form id="serviceForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label>اسم الخدمة</label>
                            <input type="text" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>فئة الخدمة</label>
                            <input type="text" name="category" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>السعر</label>
                            <input type="number" name="price" step="0.01" min="0">
                        </div>
                        <div class="form-group">
                            <label>المدة المتوقعة</label>
                            <input type="text" name="duration" placeholder="مثال: 30-45 دقيقة">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>وصف الخدمة</label>
                        <textarea name="description" rows="4"></textarea>
                    </div>
                    <div class="form-group">
                        <label>أيقونة الخدمة</label>
                        <input type="text" name="icon" placeholder="مثال: fas fa-tooth" value="fas fa-tooth">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeModal('serviceModal')">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ الخدمة</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>جاري التحميل...</p>
        </div>
    </div>

    <!-- Toast Container -->
    <div id="toastContainer" class="toast-container"></div>

    <!-- Scripts -->
    <script src="js/admin-dashboard.js"></script>
</body>
</html>
