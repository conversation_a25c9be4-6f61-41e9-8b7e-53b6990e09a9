/* Admin Dashboard Styles */

/* Reset and Base Styles */
.admin-body {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    font-family: 'Cairo', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Admin Header */
.admin-header {
    background: #d4af37;
    background: -webkit-linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    background: -moz-linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    background: -o-linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    color: white;
    padding: 1rem 0;
    -webkit-box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    -moz-box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.admin-nav {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -moz-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.admin-nav .logo {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
}

.admin-nav .logo i {
    font-size: 2rem;
    color: #ffd700;
}

.admin-actions {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

/* Global Search */
.global-search {
    position: relative;
}

.search-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
    padding: 0.5rem;
    min-width: 300px;
}

.search-input {
    background: transparent;
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    flex: 1;
    font-size: 0.9rem;
    outline: none;
}

.search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.search-btn {
    background: rgba(212, 175, 55, 0.8);
    border: none;
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    background: #d4af37;
    transform: scale(1.05);
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    max-height: 400px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    margin-top: 0.5rem;
}

.search-result-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-left: 1rem;
}

.result-content {
    flex: 1;
}

.result-title {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.result-type {
    font-size: 0.85rem;
    color: #7f8c8d;
}

.search-no-results {
    padding: 1rem;
    text-align: center;
    color: #7f8c8d;
    font-style: italic;
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.1);
    border-radius: 25px;
    font-weight: 500;
}

.admin-user i {
    font-size: 1.5rem;
}

/* Admin Sidebar */
.admin-sidebar {
    position: fixed;
    top: 80px;
    right: 0;
    width: 280px;
    height: calc(100vh - 80px);
    background: white;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1);
    z-index: 999;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    border-bottom: 1px solid #e5e7eb;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem 1.5rem;
    color: #374151;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover {
    background: #f3f4f6;
    color: #d4af37;
}

.nav-item.active .nav-link {
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    color: white;
    border-left: 4px solid #ffd700;
}

.nav-link i {
    font-size: 1.25rem;
    width: 20px;
    text-align: center;
}

/* Main Content */
.admin-main {
    margin-right: 280px;
    margin-top: 80px;
    padding: 2rem;
    min-height: calc(100vh - 80px);
}

/* Sections */
.admin-section {
    display: none;
}

.admin-section.active {
    display: block;
}

.section-header {
    margin-bottom: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h1 {
    font-size: 2.5rem;
    color: #1f2937;
    margin: 0;
    font-weight: 700;
}

.section-header p {
    color: #6b7280;
    margin: 0.5rem 0 0 0;
    font-size: 1.1rem;
}

.section-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Stats Grid */
.stats-grid {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: (minmax(250px, 1fr))[auto-fit];
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    grid-gap: 1.5rem; /* Fallback for older browsers */
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    -webkit-border-radius: 15px;
    -moz-border-radius: 15px;
    border-radius: 15px;
    -webkit-box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    -moz-box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    gap: 1.5rem;
    -webkit-transition: -webkit-transform 0.3s ease, -webkit-box-shadow 0.3s ease;
    -moz-transition: -moz-transform 0.3s ease, -moz-box-shadow 0.3s ease;
    -o-transition: -o-transform 0.3s ease, box-shadow 0.3s ease;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    -webkit-transform: translateY(-5px);
    -moz-transform: translateY(-5px);
    -ms-transform: translateY(-5px);
    -o-transform: translateY(-5px);
    transform: translateY(-5px);
    -webkit-box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    -moz-box-shadow: 0 8px 30px rgba(0,0,0,0.12);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
}

.stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #daa520 0%, #b8860b 100%);
}

.stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #ffd700 0%, #daa520 100%);
}

.stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #f4a460 0%, #cd853f 100%);
}

.stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.stat-content p {
    color: #6b7280;
    margin: 0.5rem 0 0 0;
    font-weight: 500;
}

/* Dashboard Widgets */
.dashboard-widgets {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.widget {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
}

.widget-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.widget-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.view-all {
    color: #d4af37;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
}

.view-all:hover {
    text-decoration: underline;
}

.widget-content {
    padding: 1.5rem 2rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.btn-primary {
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #b8860b 0%, #9a7209 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.btn-secondary {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background: #e5e7eb;
    color: #1f2937;
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

/* Filters */
.admin-filters {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.filter-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    align-items: end;
}

.filter-row:last-child {
    margin-bottom: 0;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    min-width: 150px;
}

.filter-group label {
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.filter-group span {
    color: #6b7280;
    font-size: 0.9rem;
    margin: 0 0.5rem;
}

/* Articles Stats */
.articles-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-item {
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Bulk Actions */
.bulk-actions {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.bulk-actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bulk-actions-buttons {
    display: flex;
    gap: 0.5rem;
}

/* Table Controls */
.table-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.table-controls-left,
.table-controls-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.table-info {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Sortable Table Headers */
.sortable {
    cursor: pointer;
    user-select: none;
    position: relative;
}

.sortable:hover {
    background-color: #f8f9fa;
}

.sortable i {
    margin-right: 0.5rem;
    opacity: 0.5;
}

.sortable.asc i:before {
    content: "\f0de";
    opacity: 1;
}

.sortable.desc i:before {
    content: "\f0dd";
    opacity: 1;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
    padding: 1rem;
}

.pagination-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.pagination-btn:hover {
    background: #f3f4f6;
}

.pagination-btn.active {
    background: #d4af37;
    color: white;
    border-color: #d4af37;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Large Modal */
.large-modal {
    max-width: 900px;
    width: 90vw;
}

/* Form Tabs */
.form-tabs {
    width: 100%;
}

.tab-buttons {
    display: flex;
    border-bottom: 2px solid #e5e7eb;
    margin-bottom: 2rem;
}

.tab-btn {
    padding: 1rem 1.5rem;
    border: none;
    background: transparent;
    color: #6b7280;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn:hover {
    color: #d4af37;
}

.tab-btn.active {
    color: #d4af37;
    border-bottom-color: #d4af37;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Editor Toolbar */
.editor-toolbar {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
}

.editor-btn {
    padding: 0.5rem;
    border: none;
    background: transparent;
    color: #6b7280;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.editor-btn:hover {
    background: #e5e7eb;
    color: #374151;
}

/* Content Stats */
.content-stats {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    font-size: 0.85rem;
    color: #6b7280;
}

/* Form Help Text */
.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: #6b7280;
    font-style: italic;
}

/* Switch Toggle */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #d4af37;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Additional Table Styles */
.article-title-cell {
    max-width: 300px;
}

.article-title-cell strong {
    display: block;
    margin-bottom: 0.25rem;
    color: #1f2937;
}

.article-meta {
    font-size: 0.8rem;
    color: #6b7280;
    line-height: 1.3;
}

.featured-badge {
    display: inline-block;
    background: #f59e0b;
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    margin-right: 0.5rem;
    font-weight: 500;
}

.category-badge {
    display: inline-block;
    background: #e5e7eb;
    color: #374151;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
}

.author-cell,
.views-cell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6b7280;
}

.author-cell i,
.views-cell i {
    color: #d4af37;
}

.date-cell {
    text-align: center;
}

.date-cell div {
    font-weight: 500;
    color: #374151;
}

.date-cell small {
    color: #6b7280;
    font-size: 0.8rem;
}

.action-buttons {
    display: flex;
    gap: 0.25rem;
    justify-content: center;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.85rem;
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn.view {
    background: #3b82f6;
    color: white;
}

.action-btn.view:hover {
    background: #2563eb;
}

.action-btn.edit {
    background: #10b981;
    color: white;
}

.action-btn.edit:hover {
    background: #059669;
}

.action-btn.duplicate {
    background: #8b5cf6;
    color: white;
}

.action-btn.duplicate:hover {
    background: #7c3aed;
}

.action-btn.delete {
    background: #ef4444;
    color: white;
}

.action-btn.delete:hover {
    background: #dc2626;
}

/* Pagination Dots */
.pagination-dots {
    padding: 0.5rem;
    color: #6b7280;
    font-weight: bold;
}

/* Loading States */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #d4af37;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design for Articles */
@media (max-width: 1200px) {
    .admin-table th:nth-child(3),
    .admin-table td:nth-child(3) {
        display: none;
    }
}

@media (max-width: 992px) {
    .filter-row {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group {
        min-width: 100%;
    }

    .articles-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .admin-table th:nth-child(4),
    .admin-table td:nth-child(4),
    .admin-table th:nth-child(6),
    .admin-table td:nth-child(6) {
        display: none;
    }
}

@media (max-width: 768px) {
    .table-controls {
        flex-direction: column;
        gap: 1rem;
    }

    .bulk-actions-header {
        flex-direction: column;
        gap: 1rem;
    }

    .bulk-actions-buttons {
        flex-wrap: wrap;
    }

    .articles-stats {
        grid-template-columns: 1fr;
    }

    .admin-table th:nth-child(5),
    .admin-table td:nth-child(5),
    .admin-table th:nth-child(7),
    .admin-table td:nth-child(7) {
        display: none;
    }

    .action-buttons {
        flex-direction: column;
    }
}

/* Enhanced Form Styles */
.form-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    display: block;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Status Badge Improvements */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-published {
    background: #d1fae5;
    color: #065f46;
}

.status-draft {
    background: #fef3c7;
    color: #92400e;
}

.status-scheduled {
    background: #dbeafe;
    color: #1e40af;
}

/* Analytics Section Styles */
.analytics-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.summary-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.summary-content h3 {
    font-size: 2rem;
    font-weight: bold;
    color: #1f2937;
    margin: 0 0 0.25rem 0;
}

.summary-content p {
    color: #6b7280;
    margin: 0 0 0.5rem 0;
    font-weight: 500;
}

.trend {
    font-size: 0.8rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.trend.positive {
    background: #d1fae5;
    color: #065f46;
}

.trend.negative {
    background: #fee2e2;
    color: #991b1b;
}

.trend.neutral {
    background: #f3f4f6;
    color: #374151;
}

/* Analytics Grid */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.analytics-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.analytics-card.large {
    grid-column: span 2;
}

.analytics-card.medium {
    grid-column: span 1;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h3 {
    margin: 0;
    color: #1f2937;
    font-size: 1.1rem;
    font-weight: 600;
}

.card-actions select {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: white;
    color: #374151;
    font-size: 0.9rem;
}

.chart-container {
    padding: 1.5rem;
    height: 300px;
    position: relative;
    max-width: 100%;
    overflow: hidden;
}

.analytics-card.large .chart-container {
    height: 400px;
    max-width: 100%;
}

.chart-container canvas {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
}

/* Analytics Tables */
.analytics-tables {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
}

.analytics-table-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    overflow: hidden;
}

.analytics-table-card h3 {
    padding: 1.5rem;
    margin: 0;
    background: #f8f9fa;
    color: #1f2937;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid #e5e7eb;
}

.analytics-table {
    width: 100%;
    border-collapse: collapse;
}

.analytics-table th,
.analytics-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #f3f4f6;
}

.analytics-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

.analytics-table td {
    color: #6b7280;
}

.analytics-table tr:hover {
    background: #f9fafb;
}

/* Responsive Analytics */
@media (max-width: 1200px) {
    .analytics-card.large {
        grid-column: span 1;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .analytics-summary {
        grid-template-columns: 1fr;
    }

    .summary-card {
        flex-direction: column;
        text-align: center;
    }

    .analytics-tables {
        grid-template-columns: 1fr;
    }

    .analytics-table {
        font-size: 0.9rem;
    }

    .analytics-table th,
    .analytics-table td {
        padding: 0.75rem 0.5rem;
    }

    .card-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .chart-container {
        height: 250px;
        padding: 1rem;
    }
}

/* Loading States for Analytics */
.analytics-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6b7280;
}

.analytics-loading i {
    margin-left: 0.5rem;
    animation: spin 1s linear infinite;
}

/* Chart Tooltips */
.chart-tooltip {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
}

/* Export Button Styles */
.export-options {
    position: relative;
    display: inline-block;
}

.export-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    z-index: 1000;
    min-width: 150px;
    display: none;
}

.export-dropdown.show {
    display: block;
}

.export-option {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.2s ease;
}

.export-option:hover {
    background: #f8f9fa;
}

.export-option:last-child {
    border-bottom: none;
}

.export-option i {
    margin-left: 0.5rem;
    color: #d4af37;
}

.filter-group input,
.filter-group select {
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    min-width: 200px;
}

.filter-group button {
    padding: 0.75rem 1rem;
    background: #d4af37;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

/* Table Styles */
.admin-table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    margin-bottom: 2rem;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 1rem 1.5rem;
    text-align: right;
    border-bottom: 1px solid #e5e7eb;
}

.admin-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.admin-table tbody tr:hover {
    background: #f9fafb;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-published {
    background: #d1fae5;
    color: #065f46;
}

.status-draft {
    background: #fef3c7;
    color: #92400e;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.action-btn.edit {
    background: #fef3c7;
    color: #d4af37;
}

.action-btn.edit:hover {
    background: #fde68a;
}

.action-btn.delete {
    background: #fee2e2;
    color: #dc2626;
}

.action-btn.delete:hover {
    background: #fecaca;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 2rem;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Content Editor */
.editor-toolbar {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border: 1px solid #d1d5db;
    border-bottom: none;
    border-radius: 8px 8px 0 0;
}

.editor-btn {
    padding: 0.5rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.editor-btn:hover {
    background: #e5e7eb;
}

.editor-btn.active {
    background: #d4af37;
    color: white;
    border-color: #d4af37;
}

.content-editor {
    min-height: 200px;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0 0 8px 8px;
    background: white;
    outline: none;
    line-height: 1.6;
}

.content-editor:focus {
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.content-editor[placeholder]:empty:before {
    content: attr(placeholder);
    color: #9ca3af;
}

/* Image Upload */
.image-upload {
    position: relative;
}

.image-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.image-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 2rem;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    background: #f9fafb;
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-preview:hover {
    border-color: #d4af37;
    background: #fffbf0;
}

.image-preview i {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1rem;
}

.image-preview span {
    color: #6b7280;
    font-weight: 500;
}

.image-preview.has-image {
    padding: 0;
    border: none;
}

.image-preview img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

/* Team Management Styles */
.team-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.team-stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    display: flex;
    align-items: center;
    gap: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.team-stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.team-stat-card:nth-child(1) .team-stat-icon {
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
}

.team-stat-card:nth-child(2) .team-stat-icon {
    background: linear-gradient(135deg, #daa520 0%, #b8860b 100%);
}

.team-stat-card:nth-child(3) .team-stat-icon {
    background: linear-gradient(135deg, #ffd700 0%, #daa520 100%);
}

.team-stat-content h3 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin: 0;
}

.team-stat-content p {
    color: #6b7280;
    margin: 0.5rem 0 0 0;
    font-weight: 500;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.team-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.team-card-header {
    position: relative;
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    padding: 2rem;
    text-align: center;
    color: white;
}

.team-card-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    overflow: hidden;
}

.team-card-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

.team-card-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
}

.team-card-specialty {
    font-size: 1rem;
    opacity: 0.9;
    margin: 0;
}

.team-card-body {
    padding: 2rem;
}

.team-card-info {
    margin-bottom: 1.5rem;
}

.team-info-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    color: #6b7280;
}

.team-info-item i {
    width: 16px;
    color: #d4af37;
}

.team-card-bio {
    color: #374151;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    font-size: 0.95rem;
}

.team-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.team-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.team-status.active {
    background: #d1fae5;
    color: #065f46;
}

.team-status.inactive {
    background: #fee2e2;
    color: #991b1b;
}

.team-actions {
    display: flex;
    gap: 0.5rem;
}

.team-card-contact {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.team-contact-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.team-contact-item i {
    width: 14px;
    color: #d4af37;
}

.team-contact-item a {
    color: #d4af37;
    text-decoration: none;
}

.team-contact-item a:hover {
    text-decoration: underline;
}

.category-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.category-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.category-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.category-info h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.category-info p {
    margin: 0.25rem 0 0 0;
    color: #6b7280;
    font-size: 0.9rem;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.category-count {
    font-weight: 600;
    color: #d4af37;
}

.category-actions {
    display: flex;
    gap: 0.5rem;
}

/* Analytics */
.analytics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.analytics-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.analytics-card h3 {
    margin: 0 0 1.5rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.chart-container {
    position: relative;
    height: 300px;
}

/* Pagination */
.admin-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.pagination-btn {
    padding: 0.75rem 1rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: #f3f4f6;
}

.pagination-btn.active {
    background: #d4af37;
    color: white;
    border-color: #d4af37;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Loading Overlay */
.loading-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.9);
    z-index: 3000;
    backdrop-filter: blur(5px);
}

.loading-overlay.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    text-align: center;
    color: #d4af37;
}

.loading-spinner i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.loading-spinner p {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 100px;
    left: 20px;
    z-index: 4000;
}

.toast {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-right: 4px solid #10b981;
}

.toast.error {
    border-right: 4px solid #ef4444;
}

.toast.warning {
    border-right: 4px solid #f59e0b;
}

.toast i {
    font-size: 1.25rem;
}

.toast.success i {
    color: #10b981;
}

.toast.error i {
    color: #ef4444;
}

.toast.warning i {
    color: #f59e0b;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Delete Confirmation */
.delete-confirmation {
    text-align: center;
    padding: 2rem 0;
}

.delete-confirmation i {
    font-size: 4rem;
    color: #ef4444;
    margin-bottom: 1rem;
}

.delete-confirmation p {
    font-size: 1.1rem;
    color: #374151;
    margin: 0;
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background-color 0.3s ease;
}

.mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Mobile Overlay */
.mobile-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    backdrop-filter: blur(2px);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .mobile-menu-toggle {
        display: block;
    }

    .admin-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
        width: 300px;
        z-index: 1001;
    }

    .admin-sidebar.active {
        transform: translateX(0);
    }

    .admin-main {
        margin-right: 0;
        padding: 1.5rem;
    }

    .dashboard-widgets {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .search-container {
        min-width: 200px;
    }

    .admin-actions {
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .admin-nav {
        padding: 0 1rem;
        flex-wrap: wrap;
    }

    .admin-nav .logo {
        font-size: 1.2rem;
    }

    .admin-nav .logo i {
        font-size: 1.5rem;
    }

    .search-container {
        min-width: 150px;
        order: 3;
        width: 100%;
        margin-top: 1rem;
    }

    .admin-user {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .admin-main {
        padding: 1rem;
        margin-top: 120px;
    }

    .section-header h1 {
        font-size: 2rem;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .section-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .stat-card {
        padding: 1.5rem;
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-content h3 {
        font-size: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .admin-filters {
        padding: 1rem;
    }

    .filter-row {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-group {
        min-width: 100%;
    }

    .categories-grid {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
        overflow-y: auto;
    }

    .modal-header {
        padding: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .admin-table-container {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    .admin-table {
        min-width: 600px;
        font-size: 0.9rem;
    }

    .admin-table th,
    .admin-table td {
        padding: 0.75rem 0.5rem;
    }

    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }

    .action-btn {
        width: 100%;
        min-width: auto;
    }
}

/* Team Management Styles */
.team-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.team-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e5e7eb;
}

.team-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.team-member-image {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.team-member-info h4 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
    text-align: center;
}

.team-member-info .specialty {
    color: #d4af37;
    font-weight: 500;
    text-align: center;
    margin-bottom: 0.25rem;
}

.team-member-info .experience {
    color: #6b7280;
    font-size: 0.9rem;
    text-align: center;
    margin-bottom: 1.5rem;
}

.team-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* Charts Container */
.charts-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.chart-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.chart-card h3 {
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    text-align: center;
}

/* User Info Styles */
.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-info i {
    font-size: 2rem;
    color: #d4af37;
}

.user-info div strong {
    display: block;
    font-weight: 600;
    color: #1f2937;
}

.user-info div small {
    color: #6b7280;
    font-size: 0.85rem;
}

/* Role Badges */
.role-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.role-badge.admin {
    background: #fee2e2;
    color: #dc2626;
}

.role-badge.manager {
    background: #fef3c7;
    color: #d4af37;
}

.role-badge.doctor {
    background: #d1fae5;
    color: #065f46;
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-badge.status-active {
    background: #d1fae5;
    color: #065f46;
}

.status-badge.status-inactive {
    background: #fee2e2;
    color: #dc2626;
}

/* Settings Styles */
.settings-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

.settings-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
}

.settings-card h3 {
    margin-bottom: 1.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    border-bottom: 2px solid #f3f4f6;
    padding-bottom: 0.5rem;
}

.settings-form .form-group {
    margin-bottom: 1.5rem;
}

.settings-form label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
}

.settings-form input,
.settings-form textarea,
.settings-form select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.3s ease;
}

.settings-form input:focus,
.settings-form textarea:focus,
.settings-form select:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

/* Alert Messages */
.alert {
    position: fixed;
    top: 100px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    z-index: 2000;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    max-width: 400px;
    animation: slideInRight 0.3s ease;
}

.alert-success {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
}

.alert-error {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #fca5a5;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Additional Responsive Improvements */
@media (max-width: 1200px) {
    .charts-container {
        grid-template-columns: 1fr;
    }

    .settings-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .team-management-grid {
        grid-template-columns: 1fr;
    }

    .services-management-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-management-card {
        margin-bottom: 1rem;
    }

    .service-card-header {
        padding: 1rem;
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .service-card-body {
        padding: 1.5rem;
    }

    .service-card-actions {
        padding: 1rem 1.5rem 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .service-card-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .filter-group {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-input,
    .filter-select {
        min-width: auto;
        width: 100%;
    }

    .filters-section {
        padding: 1rem;
    }

    .service-stats {
        flex-direction: column;
        gap: 0.75rem;
    }

    .alert {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .admin-table-container {
        overflow-x: auto;
    }

    .admin-table {
        min-width: 600px;
    }
}

/* Extra Small Devices */
@media (max-width: 480px) {
    .admin-nav {
        padding: 0 0.5rem;
    }

    .admin-nav .logo {
        font-size: 1rem;
    }

    .admin-nav .logo i {
        font-size: 1.2rem;
    }

    .search-container {
        min-width: 120px;
    }

    .search-input {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .admin-user {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
    }

    .admin-main {
        padding: 0.5rem;
        margin-top: 140px;
    }

    .section-header h1 {
        font-size: 1.5rem;
    }

    .section-header p {
        font-size: 1rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .stat-content h3 {
        font-size: 1.5rem;
    }

    .btn {
        padding: 0.6rem 1rem;
        font-size: 0.9rem;
    }

    .modal-content {
        margin: 0.5rem;
        border-radius: 8px;
    }

    .modal-header {
        padding: 0.75rem;
    }

    .modal-header h2 {
        font-size: 1.2rem;
    }

    .modal-body {
        padding: 0.75rem;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-group label {
        font-size: 0.9rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.6rem 0.8rem;
        font-size: 0.9rem;
    }

    .admin-table {
        min-width: 500px;
        font-size: 0.8rem;
    }

    .admin-table th,
    .admin-table td {
        padding: 0.5rem 0.25rem;
    }

    .action-btn {
        padding: 0.4rem;
        font-size: 0.8rem;
        min-width: 28px;
        height: 28px;
    }

    .pagination-btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .toast {
        min-width: 250px;
        font-size: 0.9rem;
    }

    .alert {
        font-size: 0.9rem;
        padding: 0.75rem 1rem;
    }
}

/* Landscape Orientation for Tablets */
@media (max-width: 1024px) and (orientation: landscape) {
    .admin-sidebar {
        width: 250px;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-widgets {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn {
        min-height: 44px;
        padding: 0.75rem 1.5rem;
    }

    .action-btn {
        min-width: 44px;
        min-height: 44px;
    }

    .nav-link {
        padding: 1.5rem;
    }

    .pagination-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .mobile-menu-toggle {
        min-height: 44px;
        min-width: 44px;
    }
}

/* Services Management Styles */
.services-management-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.service-management-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e5e7eb;
}

.service-management-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0,0,0,0.12);
}

.service-card-header {
    background: linear-gradient(135deg, #d4af37 0%, #b8860b 100%);
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: white;
}

.service-icon {
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.service-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.service-status.active {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 1px solid rgba(255,255,255,0.3);
}

.service-status.inactive {
    background: rgba(239, 68, 68, 0.2);
    color: #fee2e2;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.service-card-body {
    padding: 2rem;
}

.service-card-body h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.service-category,
.service-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    color: #6b7280;
    font-size: 0.95rem;
}

.service-category i,
.service-price i {
    color: #d4af37;
    width: 16px;
}

.service-price {
    font-weight: 600;
    color: #059669;
}

.service-stats {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.service-stats .stat {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #6b7280;
}

.service-stats .stat i {
    color: #d4af37;
    width: 14px;
}

.service-card-actions {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

/* Filters Section */
.filters-section {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-bottom: 2rem;
}

.filter-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-input,
.filter-select {
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.95rem;
    min-width: 200px;
    transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: #d4af37;
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.filter-input::placeholder {
    color: #9ca3af;
}

/* Toggle Switch */
.toggle-switch {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 0.5rem;
}

.toggle-switch input[type="checkbox"] {
    position: relative;
    width: 50px;
    height: 25px;
    appearance: none;
    background: #d1d5db;
    border-radius: 25px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.toggle-switch input[type="checkbox"]:checked {
    background: #d4af37;
}

.toggle-switch input[type="checkbox"]::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-switch input[type="checkbox"]:checked::before {
    transform: translateX(25px);
}

.toggle-label {
    font-weight: 500;
    color: #374151;
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #d4af37;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Hover Effects */
.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.nav-item:hover {
    background: rgba(212, 175, 55, 0.1);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Focus States */
.btn:focus,
.nav-link:focus {
    outline: 2px solid #d4af37;
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .admin-sidebar,
    .admin-nav,
    .alert {
        display: none !important;
    }

    .admin-main {
        margin: 0 !important;
        padding: 0 !important;
    }

    .admin-section {
        page-break-inside: avoid;
    }
}

/* Quick Analytics Styles */
.quick-analytics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.quick-chart-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    max-width: 100%;
    overflow: hidden;
}

.quick-chart-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.12);
}

.quick-chart-card h3 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
}

.quick-chart-card canvas {
    max-width: 100% !important;
    max-height: 250px !important;
    width: auto !important;
    height: auto !important;
}

.chart-footer {
    margin-top: 1rem;
    text-align: center;
    border-top: 1px solid #f0f0f0;
    padding-top: 1rem;
}

.view-detailed {
    color: #d4af37;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.view-detailed:hover {
    color: #b8941f;
    text-decoration: underline;
}

/* Quick Actions Styles */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
}

.action-card {
    background: linear-gradient(135deg, #d4af37, #f4d03f);
    color: white;
    padding: 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(212, 175, 55, 0.3);
    text-decoration: none;
}

.action-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
    color: white;
    text-decoration: none;
}

.action-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.action-content h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.action-content p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.4;
}

/* Responsive Design for Dashboard */
@media (max-width: 1200px) {
    .quick-analytics {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .quick-actions {
        grid-template-columns: 1fr;
    }

    .action-card {
        flex-direction: column;
        text-align: center;
        padding: 2rem 1.5rem;
    }

    .action-icon {
        margin-bottom: 1rem;
    }

    .quick-chart-card {
        padding: 1rem;
    }

    .quick-chart-card canvas {
        max-height: 200px;
    }
}

/* Dashboard vs Analytics Distinction */
.dashboard-section .quick-chart-card {
    border-left: 4px solid #d4af37;
}

.analytics-section .analytics-card {
    border-left: 4px solid #3b82f6;
}

/* Dashboard Overview Enhancements */
.dashboard-overview {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.dashboard-overview h2 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 1.8rem;
}

.dashboard-overview p {
    color: #6c757d;
    font-size: 1.1rem;
    margin: 0;
}
